
import json
import redis

# Connect to Redis
r = redis.Redis(host='localhost', port=6379, db=0)
print(r,type(r))

# Print all keys
keys = r.keys('*')
print(f"🔑 Found Redis keys: {[k.decode('utf-8') for k in keys]}")

# Try to fetch one known key
key = "alto_k10"
data = r.get(key)

if data:
    json_data = json.loads(data.decode('utf-8'))
    print(f"\n📦 Data for '{key}':")
    print(json.dumps(json_data, indent=2))
else:
    print(f"❌ Key '{key}' not found in Redis.")
