{"🔁Back to main menu": [{"message": "✨ Please select the services you're looking for! 🛠️💼📋💬💡 Let's get started! 🚀😊", "data": ["Purchase a Car", "Book a Service", "Tata Commercials", "True Value", "Explore More", "Contact Us"], "data_type": "list"}], "Main Menu": [{"message": "Hi 👋 Welcome to *Bhandari Automobiles*\n How can I assist you today?", "data": ["Purchase a Car", "Book a Service", "Tata Commercials"], "data_type": "button"}, {"message": "👇For more options click below 🔗✨", "data": ["Purchase a Car", "Book a Service", "True Value", "Explore More", "Contact Us"], "data_type": "list"}], "New Car Purchase": [{"message": "Great! Please select a brand:", "data": ["Maruti Suzuki", "NEXA"], "data_type": "button"}], "Maruti Suzuki": [{"message": "Here are the popular *Maruti Suzuki Arena* segment by Bhandari Automobiles.\n\nPlease choose the cars to continue?", "data": ["Alto K10", "🔁Back to main menu"], "data_type": "button"}], "NEXA": [{"message": "Here are the popular *NEXA Experience* by Bhandari Automobiles.\n\nWhich model would you like to explore?", "data": ["Baleno", "🔁Back to main menu"], "data_type": "button"}], "Tata Commercials": [{"message": "Here are the popular *Tata Commercials* segment by Bhandari Automobiles.\n\nPlease choose the vehicals to continue?", "data": ["Small Trucks", "Trucks", "Vans/Buses"], "data_type": "button"}], "Small Trucks": [{"message": "Here are the popular small trucks to select your loved one.\n\nPlease choose the below vehicals to explore?", "data": ["Ace", "Intra", "<PERSON><PERSON>"], "data_type": "button"}], "Ace": [{"message": "*Ace Variants Available*\nChoose a category to explore specific models:", "data": ["Ace EV Models", "Ace Petrol Models", "Ace Diesel Models", "Ace CNG & Bi-Fuel", "🔁Back to main menu"], "data_type": "list"}], "Ace EV Models": [{"message": "*Ace – EV Models*\nSelect your preferred electric variant:", "data": ["Ace-Pro EV", "Ace EV 1000", "Ace EV", "🔁Back to Ace"], "data_type": "list"}], "Ace Petrol Models": [{"message": "*Ace – Petrol Models*\nSelect your preferred petrol variant:", "data": ["Ace-Pro Petrol", "Ace Gold Petrol", "Ace Flex Fuel", "🔁Back to Ace"], "data_type": "list"}], "Ace Diesel Models": [{"message": "*Ace – Diesel Models*\nSelect your preferred diesel variant:", "data": ["Ace Gold Diesel", "Ace Diesel", "Ace HT+", "🔁Back to Ace"], "data_type": "list"}], "Ace CNG & Bi-Fuel": [{"message": "*Ace – CNG & Bi-Fuel Models*\nSelect a CNG or bi-fuel model:", "data": ["Ace Gold CNG+", "Ace Pro Bi-Fuel", "Ace CNG 2.0", "Ace Z<PERSON>", "🔁Back to Ace"], "data_type": "list"}], "Intra": [{"message": "*Intra Variants Available*\nChoose a category to explore specific models:", "data": ["Intra EV Models", "Intra Diesel Models", "🔁Back to main menu"], "data_type": "list"}], "Intra EV Models": [{"message": "*Intra – EV Models*\nSelect an electric variant:", "data": ["Intra EV", "🔁Back to Intra"], "data_type": "list"}], "Intra Diesel Models": [{"message": "*Intra – Diesel Models*\nChoose from the following:", "data": ["Intra V10", "Intra V20 Gold", "Intra V70 Gold", "Intra V30 Gold", "Intra V20 Winger", "Intra V50 Gold", "🔁Back to Intra"], "data_type": "list"}], "Yodha": [{"message": "*<PERSON><PERSON> V<PERSON>ts Available*\nChoose a category to explore specific models:", "data": ["<PERSON><PERSON> Models", "<PERSON><PERSON> Crew Cab Models", "<PERSON><PERSON> Models", "🔁Back to main menu"], "data_type": "list"}], "Yodha CNG Models": [{"message": "*Yodha – CNG Models*\nChoose your CNG model:", "data": ["Yodha-CNG", "🔁Back to <PERSON><PERSON>"], "data_type": "list"}], "Yodha Crew Cab Models": [{"message": "*<PERSON><PERSON> – Crew Cab Models*\nChoose your configuration:", "data": ["<PERSON><PERSON>", "Yodha EX Crew Cab", "<PERSON>dha Crew Cab 4x2", "<PERSON>dha Crew Cab 4x4", "🔁Back to <PERSON><PERSON>"], "data_type": "list"}], "Yodha Diesel Models": [{"message": "*<PERSON><PERSON> – Diesel Models*\nChoose from the following:", "data": ["Yodha 1200", "Yodha 2.0", "Yo<PERSON> 1700", "<PERSON><PERSON>", "Yodha EX Single Cab", "🔁Back to <PERSON><PERSON>"], "data_type": "list"}], "True Value": [{"message": "*True Value*, powered by Maruti Suzuki and trusted by Bhandari Automobiles, is India’s largest certified platform for buying, selling, and exchanging pre‑owned cars—designed to offer transparency, trust, and total peace of mind 🚗✅.\n\n🛠️ Why Choose True Value?\n\n🔍 120‑Point Quality Check – Every car undergoes a rigorous multi‑stage inspection by trained Maruti engineers\n🧰 Refurbishment with Genuine Parts – Ensures the car meets high safety and performance standards\n📜 Complete Certification – Each vehicle comes with a verified quality assurance certificate\n🔧 1‑Year Warranty & 🔁 3 Free Services – Post‑sale support that keeps you protected", "data": ["Buy Pre-Owned Car", "Sell your Car", "🔁Back to main menu"], "data_type": "button"}], "Buy Pre-Owned Car": [{"message": "Looking to buy a used car that feels like new? With Maruti Suzuki True Value, you get a certified, reliable, and thoroughly inspected pre‑owned vehicle—backed by the trust of India’s No.1 car brand ✅🔧.\n\n🌟 Why Buy from Maruti Suzuki True Value?\n\n🔍 120‑Point Quality Check\n🛠️ Refurbished with Genuine Maruti Parts\n📃 Certified Pre‑Owned Badge\n🔧 1‑Year Warranty & 🔁 3 Free Services\n💵 Fair Pricing & Complete Transparency\n🧑‍💼 Easy Finance & Insurance Support\n\n👇Please click on the link below to explore more:\nhttps://www.marutisuzukitruevalue.com/buy-car", "data": ["Sell your Car", "🔁Back to main menu"], "data_type": "button"}], "Sell your Car": [{"message": "Want to sell your old car without the stress and uncertainty? With Maruti Suzuki True Value, you get a transparent, quick, and fair selling process—backed by India's most trusted automobile brand 💼🚗💯.\n\n🌟 Why Sell with True Value?\n\n📲 Instant Online Evaluation\n🧑‍🔧 Expert Inspection\n📈 Best Market Price\n💬 No Middlemen or Hidden Charges\n🔁 Easy Exchange Option\n🧾 Assistance with Documentation\n\n👇Please click on the link below to explore more:\nhttps://www.marutisuzukitruevalue.com/sell-cars", "data": ["Buy Pre‑Owned Car", "🔁Back to main menu"], "data_type": "button"}], "Explore More": [{"message": "Which of the below options interests you to explore?", "data": ["Insurance Renewal", "Contact Us", "🔁Back to main menu"], "data_type": "button"}], "Insurance Renewal": [{"message": "Welcome to *Maruti Suzuki Insurance Broking*, your One Stop Shop for All Insurance Needs.\n\n*Key highlights:*\n\n☑ Dealer Assisted Towing facility\n☑ Dedicated Customer Care\n☑ Instant Policy Issuance\n☑ Near Cash‑less Accident Repairs Pan‑India\n☑ Fair and transparent Claim settlement", "data": ["Policy Renewal", "Policy Download", "Claim Process", "🔁Back to main menu"], "data_type": "list"}], "Policy Renewal": [{"message": "Please click the below link for Policy Renewal 👇\n\nhttps://www.marutisuzukiinsurance.com/Renew-Car-Insurance-Policy.aspx", "data": ["Policy Download", "Claim Process", "🔁Back to main menu"], "data_type": "button"}], "Policy Download": [{"message": "Please click the below link for Policy Download 👇\n\nhttps://www.marutisuzukiinsurance.com/PolicyDownload.aspx", "data": ["Policy Renewal", "Claim Process", "🔁Back to main menu"], "data_type": "button"}], "Claim Process": [{"message": "Follow the steps below for Claim Process:\n\n*Step 1:* Claim Intimation and Estimation\n*Step 2:* Surveyor Appointment and Loss Assessment\n\n*Step 3:* Commencement of Repairs Post Approvals\n\n*Step 4:* Vehicle Re‑Inspection and Delivery", "data": ["Policy Renewal", "Policy Download", "🔁Back to main menu"], "data_type": "button"}], "Contact Us": [{"message": "📞 Thank you for Contacting Bhandari Automobiles.\n\nHere are the key locations and contacts:\n\n📍 Arena – Newtown, Kolkata\n📞 +91 80716 46215 | 🕘 10 AM – 7 PM\n🌐 arenaofnewtownrajarhat.com\n\n📍 Arena – Nibra (Salap), Howrah\n📞 +91 80625 13689 | 🕘 10 AM – 7 PM\n🌐 arenaofnibra.com", "data": ["🔁Back to main menu"], "data_type": "button"}]}