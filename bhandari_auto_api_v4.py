import re
import os
import logging
import time
import sys
import json
import redis
from dotenv import load_dotenv
from flask import Flask, jsonify, request
from flask_cors import CORS
import google.generativeai as genai
# Removed unused import: from cars import arena_data
from tools_def import TOOL_DEF
import utility
from utility import WHATSAPP_CONFIG, load_text_file, format_all_cars_message, format_car_details_from_data, get_available_car_actions, list_all_cars_with_actions
from car_data_manager import car_data_manager, get_car_data

# ============================================================================
# CAR DATA LOADING SYSTEM - Redis Integration
# ============================================================================

# Redis connection setup
try:
    redis_client = redis.Redis(host='localhost', port=6379, db=0)
    # Test connection
    redis_client.ping()
    print("✅ Connected to Redis successfully")
except Exception as e:
    print(f"❌ Redis connection failed: {e}")
    redis_client = None

# def load_car_json(file_path):
#     """Load a car JSON file and return its content (fallback method)"""
#     try:
#         with open(file_path, 'r', encoding='utf-8') as f:
#             return json.load(f)
#     except FileNotFoundError:
#         logger.warning(f"Car file not found: {file_path}")
#         return {}
#     except json.JSONDecodeError as e:
#         logger.error(f"Error parsing JSON file {file_path}: {e}")
#         return {}

def load_car_data_from_redis(redis_key):
    """Load car data from Redis using the provided key"""
    if not redis_client:
        print(f"❌ Redis not available, cannot fetch data for key: {redis_key}")
        return {}

    try:
        data = redis_client.get(redis_key)
        if data:
            json_data = json.loads(data.decode('utf-8'))
            print(f"✅ Successfully loaded data from Redis for key: {redis_key}")
            return json_data
        else:
            print(f"❌ Key '{redis_key}' not found in Redis")
            return {}
    except Exception as e:
        print(f"❌ Error fetching data from Redis for key '{redis_key}': {e}")
        return {}

def get_car_data_from_redis_by_name(car_name):
    """
    Dynamically get car data from Redis based on car name
    Maps car names to Redis keys and fetches data on demand
    """
    # Mapping of car names to Redis keys (based on actual Redis keys available)
    car_name_to_redis_key = {
        # Arena Cars (currently available in Redis)
        'Alto K10': 'alto_k10',
        # 'Brezza': 'brezza',  # Add when available in Redis
        # 'Celerio': 'celerio',  # Add when available in Redis
        # 'Dzire': 'dzire',  # Add when available in Redis
        # 'Eeco': 'eeco',  # Add when available in Redis
        # 'Ertiga': 'ertiga',  # Add when available in Redis
        # 'S-Presso': 's_presso',  # Add when available in Redis
        # 'Swift': 'swift',  # Add when available in Redis
        # 'Wagon-R': 'wagon_r',  # Add when available in Redis

        # NEXA Cars (add when available in Redis)
        # 'Baleno': 'baleno',  # Add when available in Redis
        # 'Ciaz': 'ciaz',  # Add when available in Redis
        # 'Fronx': 'fronx',  # Add when available in Redis
        # 'Grand Vitara': 'grand_vitara',  # Add when available in Redis
        # 'Ignis': 'ignis',  # Add when available in Redis
        # 'Jimny': 'jimny',  # Add when available in Redis
        # 'XL6': 'xl6',  # Add when available in Redis

        # Tata Commercials (currently available in Redis)
        'Ace-Pro EV': 'ace-pro_ev',
        'Ace-Pro Petrol': 'ace-pro_petrol',
        'Intra V10': 'intra-v10',
        'Yodha-CNG': 'yodha-cng'
    }

    redis_key = car_name_to_redis_key.get(car_name)
    if redis_key:
        return load_car_data_from_redis(redis_key)
    else:
        print(f"❌ No Redis key mapping found for car: {car_name}")
        return {}

# Load Arena Cars from Redis (only available ones)
ALTO_K10_DATA = load_car_data_from_redis('alto_k10')
# BREZZA_DATA = load_car_data_from_redis('brezza')  # Add when available in Redis
# CELERIO_DATA = load_car_data_from_redis('celerio')  # Add when available in Redis
# DZIRE_DATA = load_car_data_from_redis('dzire')  # Add when available in Redis
# EECO_DATA = load_car_data_from_redis('eeco')  # Add when available in Redis
# ERTIGA_DATA = load_car_data_from_redis('ertiga')  # Add when available in Redis
# S_PRESSO_DATA = load_car_data_from_redis('s_presso')  # Add when available in Redis
# SWIFT_DATA = load_car_data_from_redis('swift')  # Add when available in Redis
# WAGON_R_DATA = load_car_data_from_redis('wagon_r')  # Add when available in Redis

# Load NEXA Cars from Redis (add when available)
# BALENO_DATA = load_car_data_from_redis('baleno')  # Add when available in Redis
# CIAZ_DATA = load_car_data_from_redis('ciaz')  # Add when available in Redis
# FRONX_DATA = load_car_data_from_redis('fronx')  # Add when available in Redis
# GRAND_VITARA_DATA = load_car_data_from_redis('grand_vitara')  # Add when available in Redis
# IGNIS_DATA = load_car_data_from_redis('ignis')  # Add when available in Redis
# JIMNY_DATA = load_car_data_from_redis('jimny')  # Add when available in Redis
# XL6_DATA = load_car_data_from_redis('xl6')  # Add when available in Redis

# Load Tata Commercials from Redis (available ones)
ACE_GOLD_CNG_PLUS_DATA = load_car_data_from_redis('ace_gold_cng_plus')
TATA_ACE_GOLD_PETROL_DATA = load_car_data_from_redis('tata_ace_gold_petrol')
TATA_ACE_FLEX_FUEL_DATA = load_car_data_from_redis('tata_ace_flex_fuel')
MAIN_MENU_DATA = load_car_data_from_redis('main_menu')
ACE_PRO_BI_FUEL_DATA = load_car_data_from_redis('ace_pro_bi–fuel')
ACE_PRO_PETROL_DATA = load_car_data_from_redis('ace_pro_petrol')
ACE_CNG_2_0_BI_FUEL_DATA = load_car_data_from_redis('ace_cng_2.0(bi–fuel)')
TATA_ACE_ZIP_DATA = load_car_data_from_redis('tata_ace_zip')
TATA_INTRA_EV_DATA = load_car_data_from_redis('tata_intra_ev')
TATA_YODHA_CREW_CAB_DATA = load_car_data_from_redis('tata_yodha_crew_cab')
ACE_EV_1000_DATA = load_car_data_from_redis('ace_ev_1000')
TATA_INTRA_V20_GOLD_DATA = load_car_data_from_redis('tata_intra_v20_gold')
TATA_YODHA_1200_DATA = load_car_data_from_redis('tata_yodha_1200')
YODHA_EX_CREW_CAB_DATA = load_car_data_from_redis('yodha_ex_crew_cab')
TATA_ACE_EV_DATA = load_car_data_from_redis('tata_ace_ev')
TATA_YODHA_2_0_DATA = load_car_data_from_redis('tata_yodha_2.0')
TATA_ACE_HT_PLUS_DATA = load_car_data_from_redis('tata_ace_ht+')
YODHA_CREW_CAB_4X2_DATA = load_car_data_from_redis('yodha_crew_cab_4×2')
TATA_YODHA_EX_DATA = load_car_data_from_redis('tata_yodha_ex')
TATA_ACE_GOLD_DIESEL_DATA = load_car_data_from_redis('tata_ace_gold_diesel')
YODHA_CREW_CAB_4X4_DATA = load_car_data_from_redis('yodha_crew_cab_4×4')
ACE_PRO_EV_DATA = load_car_data_from_redis('ace_pro_ev')
TATA_INTRA_V70_GOLD_DATA = load_car_data_from_redis('tata_intra_v70_gold')
TATA_YODHA_1700_DATA = load_car_data_from_redis('tata_yodha_1700')
YODHA_EX_SINGLE_CAB_DATA = load_car_data_from_redis('yodha_ex_single_cab')
INTRA_V20_WINGER_DATA = load_car_data_from_redis('intra_v20_winger')
TATA_INTRA_V30_GOLD_DATA = load_car_data_from_redis('tata_intra_v30_gold')
TATA_INTRA_V50_GOLD_DATA = load_car_data_from_redis('tata_intra_v50_gold')
TATA_ACE_DIESEL_DATA = load_car_data_from_redis('tata_ace_diesel')
ALTO_K10_DATA = load_car_data_from_redis('alto_k10')
YODHA_CNG_DATA = load_car_data_from_redis('yodha_cng')
TATA_INTRA_V10_DATA = load_car_data_from_redis('tata_intra_v10')



# Load main menu from Redis (fallback to JSON if not in Redis)
MAIN_MENU_DATA = load_car_data_from_redis('main_menu')
# if not MAIN_MENU_DATA:
#     MAIN_MENU_DATA = load_car_json('JSON/main_menu.json')

# Create a unified car data dictionary for easy access (only loaded cars)
CAR_DATA_REGISTRY = {
    # Arena Cars (currently available from Redis)
    'Alto K10': ALTO_K10_DATA,
    # 'Brezza': BREZZA_DATA,  # Add when available in Redis
    # 'Celerio': CELERIO_DATA,  # Add when available in Redis
    # 'Dzire': DZIRE_DATA,  # Add when available in Redis
    # 'Eeco': EECO_DATA,  # Add when available in Redis
    # 'Ertiga': ERTIGA_DATA,  # Add when available in Redis
    # 'S-Presso': S_PRESSO_DATA,  # Add when available in Redis
    # 'Swift': SWIFT_DATA,  # Add when available in Redis
    # 'Wagon-R': WAGON_R_DATA,  # Add when available in Redis

    # NEXA Cars (add when available in Redis)
    # 'Baleno': BALENO_DATA,  # Add when available in Redis
    # 'Ciaz': CIAZ_DATA,  # Add when available in Redis
    # 'Fronx': FRONX_DATA,  # Add when available in Redis
    # 'Grand Vitara': GRAND_VITARA_DATA,  # Add when available in Redis
    # 'Ignis': IGNIS_DATA,  # Add when available in Redis
    # 'Jimny': JIMNY_DATA,  # Add when available in Redis
    # 'XL6': XL6_DATA,  # Add when available in Redis

    # Tata Commercials (currently available from Redis)
    'Ace-Pro EV': ACE_PRO_EV_DATA,
    'Ace-Pro Petrol': ACE_PRO_PETROL_DATA,
    'Intra V10': TATA_INTRA_V10_DATA,
    'Yodha-CNG': YODHA_CNG_DATA,
    'Ace Gold CNG+': ACE_GOLD_CNG_PLUS_DATA,
    'Ace Gold Petrol': TATA_ACE_GOLD_PETROL_DATA,
    'Ace Flex Fuel': TATA_ACE_FLEX_FUEL_DATA,
    'Ace Pro Bi-Fuel': ACE_PRO_BI_FUEL_DATA,
    'Ace CNG 2.0': ACE_CNG_2_0_BI_FUEL_DATA,
    'Ace Zip': TATA_ACE_ZIP_DATA,
    'Intra EV': TATA_INTRA_EV_DATA,
    'Yodha Crew Cab': TATA_YODHA_CREW_CAB_DATA,
    'Ace EV 1000': ACE_EV_1000_DATA,
    'Intra V20 Gold': TATA_INTRA_V20_GOLD_DATA,
    'Yodha 1200': TATA_YODHA_1200_DATA,
    'Yodha EX Crew Cab': YODHA_EX_CREW_CAB_DATA,
    'Ace EV': TATA_ACE_EV_DATA,
    'Yodha 2.0': TATA_YODHA_2_0_DATA,
    'Ace HT+': TATA_ACE_HT_PLUS_DATA,
    'Yodha Crew Cab 4x2': YODHA_CREW_CAB_4X2_DATA,
    'Yodha EX': TATA_YODHA_EX_DATA,
    'Ace Gold Diesel': TATA_ACE_GOLD_DIESEL_DATA,
    'Yodha Crew Cab 4x4': YODHA_CREW_CAB_4X4_DATA,
    'Intra V70 Gold': TATA_INTRA_V70_GOLD_DATA,
    'Yodha 1700': TATA_YODHA_1700_DATA,
    'Yodha EX Single Cab': YODHA_EX_SINGLE_CAB_DATA,
    'Intra V20 Winger': INTRA_V20_WINGER_DATA,
    'Intra V30 Gold': TATA_INTRA_V30_GOLD_DATA,
    'Intra V50 Gold': TATA_INTRA_V50_GOLD_DATA,
    'Ace Diesel': TATA_ACE_DIESEL_DATA,
    'Alto K10': ALTO_K10_DATA,



    # Main Menu
    'Main Menu': MAIN_MENU_DATA,
}

# Alternative name mappings to handle different naming conventions
CAR_NAME_MAPPINGS = {
    'alto k10': 'Alto K10',
    'alto': 'Alto K10',
    'k10': 'Alto K10',
    # 'brezza': 'Brezza',
    # 'vitara brezza': 'Brezza',
    # 'celerio': 'Celerio',
    # 'dzire': 'Dzire',
    # 'desire': 'Dzire',
    # 'eeco': 'Eeco',
    # 'ertiga': 'Ertiga',
    # 's-presso': 'S-Presso',
    # 's presso': 'S-Presso',
    # 'spresso': 'S-Presso',
    # 'swift': 'Swift',
    # 'wagon-r': 'Wagon-R',
    # 'wagon r': 'Wagon-R',
    # 'wagonr': 'Wagon-R',
    # 'baleno': 'Baleno',
    # 'ciaz': 'Ciaz',
    # 'fronx': 'Fronx',
    # 'grand vitara': 'Grand Vitara',
    # 'vitara': 'Grand Vitara',
    # 'ignis': 'Ignis',
    # 'jimny': 'Jimny',
    # 'xl6': 'XL6',
    # 'xl-6': 'XL6'

    # Tata Commercials
    'ace pro ev': 'Ace-Pro EV',
    'ace-pro ev': 'Ace-Pro EV',
    'ace pro electric': 'Ace-Pro EV',
    'ace pro petrol': 'Ace-Pro Petrol',
    'ace-pro petrol': 'Ace-Pro Petrol',
    'ace pro': 'Ace-Pro Petrol',  # Default to petrol variant
    'intra v10': 'Intra V10',
    'intra-v10': 'Intra V10',
    'intra': 'Intra V10',
    'yodha cng': 'Yodha-CNG',
    'yodha-cng': 'Yodha-CNG',
    'yodha': 'Yodha-CNG'
}

def get_price_key_for_car(car_data):
    """
    Get the correct price key for a car's variant data
    Handles different naming conventions across JSON files
    """
    possible_price_keys = [
        'Ex-showroom Price',      # Most Arena cars (hyphen)
        'Ex-showroom Price',      # Some cars (en-dash)
        'Ex-Showroom Prices',     # Some NEXA cars (hyphen + plural)
        'Ex-Showroom Prices',     # Variation with en-dash and plural
        'Variants',               # Alternative naming
        'Price List'              # Alternative naming
    ]

    for key in possible_price_keys:
        if key in car_data:
            return key

    return None

def get_car_variants_unified(car_data):
    """
    Get car variants with unified handling of different price key formats
    Works with both old JSON format and new arena_data.py/nexa_data.py format
    """
    # Try to find variants key in new format (from arena_data.py/nexa_data.py)
    for key in car_data.keys():
        if key.endswith(' variants'):
            variants = car_data[key]
            if isinstance(variants, list):
                return variants

    # Fallback to old format
    price_key = get_price_key_for_car(car_data)

    if not price_key:
        return []

    price_data = car_data.get(price_key, [])
    if not price_data or len(price_data) == 0:
        return []

    variants = price_data[0].get('data', [])
    return variants

def get_car_data_by_name(car_name):
    """Get car data by name with Redis as primary source, fallback to registry and name mappings"""
    # First try to get data from Redis
    redis_data = get_car_data_from_redis_by_name(car_name)
    if redis_data:
        return redis_data

    # Fallback to registry lookup
    if car_name in CAR_DATA_REGISTRY:
        return CAR_DATA_REGISTRY[car_name]

    # Try name mapping
    normalized_name = car_name.lower().strip()
    if normalized_name in CAR_NAME_MAPPINGS:
        mapped_name = CAR_NAME_MAPPINGS[normalized_name]
        # Try Redis first for mapped name
        redis_data = get_car_data_from_redis_by_name(mapped_name)
        if redis_data:
            return redis_data
        # Fallback to registry
        return CAR_DATA_REGISTRY.get(mapped_name, {})

    return {}

def get_car_response_with_media_id(car_name, action_key=None):
    """Get car response with media_id support"""
    car_data = get_car_data_by_name(car_name)

    if not car_data:
        return None

    # Get the proper car name (handle name mappings)
    proper_car_name = car_name
    if car_name in CAR_DATA_REGISTRY:
        proper_car_name = car_name
    else:
        # Check if it's a mapped name
        normalized_name = car_name.lower().strip()
        if normalized_name in CAR_NAME_MAPPINGS:
            proper_car_name = CAR_NAME_MAPPINGS[normalized_name]

    # If action_key is provided, look for specific action
    if action_key and action_key in car_data:
        response_data = car_data[action_key]
    # Otherwise, look for the main car entry using proper name
    elif proper_car_name in car_data:
        response_data = car_data[proper_car_name]
    else:
        # Fallback: get first available entry
        first_key = next(iter(car_data.keys()), None)
        if first_key:
            response_data = car_data[first_key]
        else:
            return None

    if not response_data or not isinstance(response_data, list) or len(response_data) == 0:
        return None

    # Handle gallery sections (Exterior/Interior) that have multiple items
    if len(response_data) > 1 and action_key in ['Exterior', 'Interior', 'Gallery']:
        # For gallery sections, combine the image data with navigation buttons
        first_item = response_data[0]
        second_item = response_data[1] if len(response_data) > 1 else {}

        # Get image data from first item
        image_message = first_item.get("message", "")
        image_data = first_item.get("data", [])
        image_data_type = first_item.get("data_type", "image")
        media_id = first_item.get("Media_ID", "")

        # Get navigation buttons from second item
        nav_message = second_item.get("message", "")
        nav_data = second_item.get("data", [])
        nav_data_type = second_item.get("data_type", "button")

        # Use original messages without enhancement
        combined_message = f"{image_message}\n\n{nav_message}"

        response = {
            "status": "success",
            "car_name": proper_car_name,
            "message": combined_message,
            "data": image_data,
            "data_type": image_data_type,
            "buttons": [{
                "data": image_data,
                "data_type": image_data_type,
                "message": image_message
            }, {
                "data": nav_data,
                "data_type": nav_data_type,
                "message": nav_message
            }],
            "hasButtons": len(nav_data) > 0
        }

        # Add media_id if present
        if media_id:
            response["media_id"] = media_id
            response["buttons"][0]["media_id"] = media_id

    else:
        # Handle regular sections (single item)
        step_config = response_data[0]
        message = step_config.get("message", "")
        data = step_config.get("data", [])
        data_type = step_config.get("data_type", "list")
        media_id = step_config.get("Media_ID", "")  # Extract Media_ID if present

        response = {
            "status": "success",
            "car_name": proper_car_name,
            "message": message,
            "data": data,
            "data_type": data_type,
            "buttons": [{
                "data": data,
                "data_type": data_type,
                "message": message
            }],
            "hasButtons": len(data) > 0
        }

        # Add media_id if present
        if media_id:
            response["media_id"] = media_id
            response["buttons"][0]["media_id"] = media_id

    return response

load_dotenv()
app = Flask(__name__)
app.config["DEBUG"] = False
CORS(app)

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')
def load_system_prompt():
    try:
        with open('system_prompt.txt', 'r', encoding='utf-8') as f:
            return f.read().strip()
    except FileNotFoundError:
        return "You are a helpful car dealership assistant for Bhandari Automobiles. Help customers with car information, bookings, and dealership services."

def create_enhanced_system_prompt(base_prompt, knowledge_base_content):
    """Create enhanced system prompt with knowledge base content"""
    if knowledge_base_content:
        enhanced_prompt = f"""{base_prompt}

## KNOWLEDGE BASE
You have access to the following specific information about Bhandari Automobiles:

{knowledge_base_content}

**IMPORTANT**: When customers ask questions about details,addresses, contact information, hours, or other specific details, use the information from the Knowledge Base above to provide accurate answers. Don't just redirect to menus - answer their questions directly first, then offer additional help if appropriate.
"""
        return enhanced_prompt
    return base_prompt

base_system_prompt = load_system_prompt()

def load_information_file():
    """Load information from text files for knowledge base"""
    try:
        # Load knowledge base
        knowledge_base = load_text_file("knowledge_base.txt")

        # You can also load information.txt if it exists
        information = load_text_file("information.txt")

        # Combine both if needed
        combined_info = ""
        if knowledge_base:
            combined_info += f"Knowledge Base:\n{knowledge_base}\n\n"
        return combined_info.strip()
    except Exception as e:
        print(f"Error loading information files: {e}")
        return ""

# Load knowledge base and information
knowledge_base_content = load_information_file()

# Create enhanced system prompt with knowledge base
system_prompt = create_enhanced_system_prompt(base_system_prompt, knowledge_base_content)

# Initialize Gemini AI
if GEMINI_API_KEY:
    genai.configure(api_key=GEMINI_API_KEY)

model = genai.GenerativeModel(
    model_name="models/gemini-2.0-flash",
    tools=TOOL_DEF,
    system_instruction=system_prompt
) if GEMINI_API_KEY else None
SESSION_ID = {}
# Store user context for each session
USER_CONTEXT = {}

# Simple rate limiting for AI calls
last_ai_call_time = 0
AI_CALL_INTERVAL = 2  # Minimum seconds between AI calls

def search_knowledge_base(query):
    """Search knowledge base for specific information - simplified approach"""
    if not knowledge_base_content:
        return None

    query_lower = query.lower()
    lines = knowledge_base_content.split('\n')

    # Look for relevant Q&A pairs with scoring for better matching
    best_match = None
    best_score = 0

    for i, line in enumerate(lines):
        if line.startswith('Q:'):
            line_lower = line.lower()

            # Calculate relevance score
            score = 0
            query_words = query_lower.split()

            # Give higher score for exact matches of important keywords
            if 'arena' in query_lower and 'arena' in line_lower:
                score += 10
            elif 'nexa' in query_lower and 'nexa' in line_lower:
                score += 10

            # Score for other matching words
            for word in query_words:
                if len(word) > 2 and word in line_lower:  # Skip short words like "of", "is"
                    score += 1

            # If this is a better match, store it
            if score > best_score and score > 2:  # Minimum threshold
                if i + 1 < len(lines) and lines[i + 1].startswith('A:'):
                    question = line[2:].strip()  # Remove 'Q:'
                    answer = lines[i + 1][2:].strip()  # Remove 'A:'

                    # Get additional lines that are part of the answer
                    full_answer = answer
                    j = i + 2
                    while j < len(lines) and not lines[j].startswith('Q:') and lines[j].strip():
                        full_answer += '\n' + lines[j].strip()
                        j += 1

                    best_match = {
                        "status": "success",
                        "message": f"📍 **{question}**\n\n{full_answer}",
                        "source": "knowledge_base"
                    }
                    best_score = score

    return best_match

# Removed complex mapping functions - let LLM handle natural language understanding

def get_arena_and_nexa_cars():
    """Get properly categorized Arena and NEXA cars from registry"""

    # Hardcoded correct categorization based on actual Maruti Suzuki lineup
    arena_cars = ["Alto K10", "Swift", "Dzire", "Brezza", "Celerio", "Eeco", "Ertiga", "S-Presso", "Wagon-R"]
    nexa_cars = ["Baleno", "Fronx", "Grand Vitara", "Ignis", "Ciaz", "Jimny", "XL6"]

    # Filter to only include cars that exist in our registry
    available_arena = []
    available_nexa = []

    for car_name in CAR_DATA_REGISTRY.keys():
        if car_name == 'Main Menu':
            continue
        if car_name in arena_cars:
            available_arena.append(car_name)
        elif car_name in nexa_cars:
            available_nexa.append(car_name)

    return available_arena, available_nexa

def handle_vehicle_type_queries(prompt_lower, user_id):
    """Handle vehicle type queries like 'I need a van', 'recommend a truck', etc."""

    # Define vehicle type mappings
    vehicle_types = {
        'van': ['Ace-Pro EV', 'Ace-Pro Petrol', 'Intra V10', 'Yodha-CNG'],  # For now, recommend commercial vehicles for van queries
        'truck': ['Ace-Pro EV', 'Ace-Pro Petrol', 'Intra V10', 'Yodha-CNG'],
        'lorry': ['Ace-Pro EV', 'Ace-Pro Petrol', 'Intra V10', 'Yodha-CNG'],
        'commercial': ['Ace-Pro EV', 'Ace-Pro Petrol', 'Intra V10', 'Yodha-CNG'],
        'cargo': ['Ace-Pro EV', 'Ace-Pro Petrol', 'Intra V10', 'Yodha-CNG'],
        'delivery': ['Ace-Pro EV', 'Ace-Pro Petrol', 'Intra V10', 'Yodha-CNG'],
        'business': ['Ace-Pro EV', 'Ace-Pro Petrol', 'Intra V10', 'Yodha-CNG'],
        'mini truck': ['Ace-Pro EV', 'Ace-Pro Petrol'],
        'pickup': ['Yodha-CNG'],
        'electric': ['Ace-Pro EV'],
        'ev': ['Ace-Pro EV'],
        'cng': ['Yodha-CNG'],
        'petrol truck': ['Ace-Pro Petrol'],
        'diesel truck': ['Intra V10']
    }

    # Check for vehicle type queries
    for vehicle_type, vehicles in vehicle_types.items():
        if vehicle_type in prompt_lower:
            # Filter vehicles that exist in our registry
            available_vehicles = [v for v in vehicles if v in CAR_DATA_REGISTRY]

            if available_vehicles:
                if vehicle_type in ['van']:
                    message = f"🚐 **Perfect! We have the ideal van for you:**\n\n"
                elif vehicle_type in ['truck', 'lorry', 'cargo', 'delivery']:
                    message = f"🚛 **Great choice! Here are our commercial trucks:**\n\n"
                elif vehicle_type in ['commercial', 'business']:
                    message = f"🏢 **Excellent! Here are our commercial vehicles:**\n\n"
                elif vehicle_type in ['electric', 'ev']:
                    message = f"⚡ **Going Green! Here's our electric commercial vehicle:**\n\n"
                elif vehicle_type in ['cng']:
                    message = f"🌿 **Eco-friendly choice! Here's our CNG commercial vehicle:**\n\n"
                else:
                    message = f"🚗 **Here are the vehicles matching your needs:**\n\n"

                # Add vehicle descriptions
                for vehicle in available_vehicles:
                    if vehicle == 'Ace-Pro EV':
                        message += "⚡ **Ace-Pro EV** - Electric mini-truck, zero emissions, perfect for urban deliveries\n"
                    elif vehicle == 'Ace-Pro Petrol':
                        message += "⛽ **Ace-Pro Petrol** - Fuel-efficient mini-truck, reliable for city logistics\n"
                    elif vehicle == 'Intra V10':
                        message += "🔧 **Intra V10** - Powerful diesel truck, high payload capacity for heavy-duty work\n"
                    elif vehicle == 'Yodha-CNG':
                        message += "🌿 **Yodha-CNG** - Eco-friendly CNG pickup, cost-effective for long-distance transport\n"
                    elif vehicle == 'Eeco':
                        message += "🚐 **Eeco** - Versatile multi-purpose vehicle, perfect for passenger and cargo needs\n"

                message += f"\n💡 **Why choose our commercial vehicles?**\n"
                message += "• Proven reliability and durability\n"
                message += "• Extensive service network across India\n"
                message += "• Competitive pricing and financing options\n"
                message += "• Expert after-sales support\n\n"
                message += "*Click below to explore detailed specifications:*"

                return {
                    "message": message,
                    "buttons": [{"data": available_vehicles, "data_type": "list", "message": "Select a vehicle to explore:"}],
                    "function_response": []
                }

    return None

def handle_category_queries(prompt_lower, user_id):
    """Handle category-based queries like 'arena cars', 'nexa cars', etc."""

    arena_cars, nexa_cars = get_arena_and_nexa_cars()

    if any(phrase in prompt_lower for phrase in ["arena cars", "arena models", "maruti suzuki arena"]):
        return {
            "message": f"🏟️ **Arena Cars - Trusted & Reliable**\n\nOur Arena lineup offers dependable, value-for-money vehicles:\n\n" +
                      "\n".join([f"🚗 {car}" for car in arena_cars[:9]]) +
                      f"\n\n📋 Total: {len(arena_cars)} models available\n\n*Which car interests you?*",
            "buttons": [{"data": arena_cars[:9], "data_type": "list", "message": "Select an Arena car:"}],
            "function_response": []
        }

    elif any(phrase in prompt_lower for phrase in ["nexa cars", "nexa models", "premium cars"]):
        return {
            "message": f"🌟 **NEXA Cars - Premium & Stylish**\n\nOur NEXA collection features premium vehicles with advanced features:\n\n" +
                      "\n".join([f"✨ {car}" for car in nexa_cars]) +
                      f"\n\n📋 Total: {len(nexa_cars)} models available\n\n*Which premium car would you like to explore?*",
            "buttons": [{"data": nexa_cars, "data_type": "list", "message": "Select a NEXA car:"}],
            "function_response": []
        }

    elif any(phrase in prompt_lower for phrase in ["all cars", "show me all", "complete car list", "all models"]):
        total_cars = len(arena_cars) + len(nexa_cars)
        return {
            "message": f"🚗 **Complete Maruti Suzuki Range**\n\n🏟️ **Arena Cars** ({len(arena_cars)} models):\n{', '.join(arena_cars[:4])}...\n\n🌟 **NEXA Cars** ({len(nexa_cars)} models):\n{', '.join(nexa_cars[:4])}...\n\n📋 **Total Portfolio**: {total_cars} models\n\n*Choose a category or specific car:*",
            "buttons": [{"data": ["Arena Cars", "NEXA Cars"] + arena_cars[:3] + nexa_cars[:3], "data_type": "list", "message": "Explore our range:"}],
            "function_response": []
        }

    return None

def handle_feature_queries(prompt_lower, user_id):
    """Handle feature-specific queries like 'swift features', 'baleno safety', etc."""

    # Feature mappings
    feature_mappings = {
        "features": "alto_k10 features",
        "safety": "More about this",
        "interior": "More about this",
        "technology": "More about this",
        "specifications": "More about this",
        "details": "More about this"
    }

    # Check if it's a car + feature query
    for car_name in CAR_DATA_REGISTRY.keys():
        if car_name != 'Main Menu' and car_name.lower() in prompt_lower:
            # Check for feature keywords
            for feature_key, action in feature_mappings.items():
                if feature_key in prompt_lower:
                    # Try to get the car's "More about this" action
                    car_data = get_car_data_by_name(car_name)
                    if car_data and action in car_data:
                        logger.info(f"✅ Feature query match: '{prompt_lower}' -> {car_name} -> {action}")
                        return get_car_response_with_media_id(car_name, action)
            break

    return None

def handle_price_based_queries(prompt_lower, user_id):
    """Handle price-based queries like 'cars under 10 lakhs', 'budget cars', etc."""

    # Extract price limit from query and determine query type
    price_limit = None
    query_type = "under"  # default

    # Look for price patterns
    import re

    # Patterns for "under/below/budget" queries
    under_patterns = [
        r'under\s+(\d+)\s*l',
        r'under\s+(\d+)\s*lakh',
        r'under\s+(\d+)\s*lakhs',
        r'below\s+(\d+)\s*l',
        r'below\s+(\d+)\s*lakh',
        r'below\s+(\d+)\s*lakhs',
        r'budget\s+(\d+)\s*l',
        r'budget\s+(\d+)\s*lakh',
        r'budget\s+(\d+)\s*lakhs'
    ]

    # Patterns for "from/starting" queries
    from_patterns = [
        r'from\s+(\d+)\s*l',
        r'from\s+(\d+)\s*lakh',
        r'from\s+(\d+)\s*lakhs',
        r'starting\s+(\d+)\s*l',
        r'starting\s+(\d+)\s*lakh',
        r'starting\s+(\d+)\s*lakhs'
    ]

    # Check for "under" type queries first
    for pattern in under_patterns:
        match = re.search(pattern, prompt_lower)
        if match:
            price_limit = float(match.group(1))
            query_type = "under"
            break

    # If no "under" match, check for "from" type queries
    if not price_limit:
        for pattern in from_patterns:
            match = re.search(pattern, prompt_lower)
            if match:
                price_limit = float(match.group(1))
                query_type = "from"
                break

    if not price_limit:
        return None

    # Search for cars within the price limit using arena_data.py and nexa_data.py
    matching_cars = []

    try:
        # Import both arena and nexa data
        import sys
        sys.path.insert(0, 'cars')
        from arena_data import cars_data as arena_cars
        from nexa_data import cars_data as nexa_cars

        # Check Arena cars
        for car_key, car_data in arena_cars.items():
            car_name = car_data.get('name', '')
            variants_key = f"{car_key} variants"

            if variants_key in car_data:
                variants = car_data[variants_key]
                car_prices = []

                for variant in variants:
                    price_str = variant.get('price', '')
                    if price_str:
                        # Extract numeric price (remove ₹ and Lakh)
                        price_match = re.search(r'(\d+\.?\d*)', price_str)
                        if price_match:
                            car_prices.append(float(price_match.group(1)))

                # Check if any variant matches the price criteria
                price_match = False
                if query_type == "under":
                    price_match = car_prices and min(car_prices) <= price_limit
                elif query_type == "from":
                    price_match = car_prices and min(car_prices) >= price_limit

                if price_match:
                    min_price = min(car_prices)
                    max_price = max(car_prices)
                    matching_cars.append({
                        'name': car_name,
                        'min_price': min_price,
                        'max_price': max_price,
                        'price_range': f"₹{min_price:.2f} - ₹{max_price:.2f} Lakh",
                        'dealership': 'Arena'
                    })

        # Check NEXA cars
        for car_key, car_data in nexa_cars.items():
            car_name = car_data.get('name', '')
            variants_key = f"{car_key} variants"

            if variants_key in car_data:
                variants = car_data[variants_key]
                car_prices = []

                for variant in variants:
                    price_str = variant.get('price', '')
                    if price_str:
                        # Extract numeric price (remove ₹ and Lakh)
                        price_match = re.search(r'(\d+\.?\d*)', price_str)
                        if price_match:
                            car_prices.append(float(price_match.group(1)))

                # Check if any variant matches the price criteria
                price_match = False
                if query_type == "under":
                    price_match = car_prices and min(car_prices) <= price_limit
                elif query_type == "from":
                    price_match = car_prices and min(car_prices) >= price_limit

                if price_match:
                    min_price = min(car_prices)
                    max_price = max(car_prices)
                    matching_cars.append({
                        'name': car_name,
                        'min_price': min_price,
                        'max_price': max_price,
                        'price_range': f"₹{min_price:.2f} - ₹{max_price:.2f} Lakh",
                        'dealership': 'NEXA'
                    })

    except Exception as e:
        logger.error(f"Error loading car data for price queries: {e}")
        return None

    if not matching_cars:
        return None

    # Sort by minimum price
    matching_cars.sort(key=lambda x: x['min_price'])

    # Create response message based on query type
    if query_type == "under":
        message = f"🚗 **Cars Under ₹{price_limit:.0f} Lakh**\n\n"
        message += f"Found {len(matching_cars)} cars within your budget:\n\n"
    else:  # query_type == "from"
        message = f"🚗 **Cars Starting From ₹{price_limit:.0f} Lakh**\n\n"
        message += f"Found {len(matching_cars)} cars in this price range:\n\n"

    for i, car in enumerate(matching_cars[:8], 1):  # Limit to 8 cars
        message += f"{i}. **{car['name']}** ({car['dealership']})\n"
        message += f"   💰 {car['price_range']}\n\n"

    if len(matching_cars) > 8:
        message += f"...and {len(matching_cars) - 8} more cars\n\n"

    # message += "*Click on any car below to explore:*"

    # Create buttons with car names
    car_names = [car['name'] for car in matching_cars[:8]]

    return {
        "message": message,
        "buttons": [{"data": car_names, "data_type": "list", "message": "Select a car to explore:"}],
        "function_response": []
    }

def check_whatsapp_flow_match(prompt, user_id=None):
    """
    Check if the prompt matches any WhatsApp flow step using NEW CAR DATA SYSTEM
    Prioritizes exact matches and car-specific actions

    Args:
        prompt (str): User input prompt
        user_id (str): User ID for context lookup

    Returns:
        dict or None: WhatsApp response if match found, None otherwise
    """
    prompt_clean = prompt.strip()
    prompt_lower = prompt_clean.lower()

    # PRIORITY 1: Check for exact matches in main menu first (to avoid conflicts with car name mappings)
    if prompt_clean in MAIN_MENU_DATA:
        logger.info(f"✅ Main menu match (priority): '{prompt}' -> {prompt_clean}")
        return get_whatsapp_response(prompt_clean, user_id)

    # Case-insensitive main menu matches
    for key in MAIN_MENU_DATA.keys():
        if key.lower() == prompt_clean.lower():
            logger.info(f"✅ Main menu match (case-insensitive priority): '{prompt}' -> {key}")
            return get_whatsapp_response(key, user_id)

    # PRIORITY 2: Direct car name match (after main menu check)
    car_response = get_car_response_with_media_id(prompt_clean)
    if car_response:
        proper_car_name = car_response.get('car_name', prompt_clean)
        logger.info(f"✅ Direct car match: '{prompt}' -> {proper_car_name}")
        # Set user context for direct car matches using proper car name
        if user_id:
            set_user_context(user_id, proper_car_name)
            logger.info(f"🎯 Set user context: {user_id} -> {proper_car_name}")
        return car_response

    # PRIORITY 3: Check for car actions using new system - CONTEXT FIRST
    # First, check if user has a current car context and the action exists in that car
    if user_id:
        current_car = get_user_context(user_id)
        if current_car:
            car_data = get_car_data_by_name(current_car)

            # Try exact match first
            if car_data and prompt_clean in car_data:
                response = get_car_response_with_media_id(current_car, prompt_clean)
                if response:
                    logger.info(f"✅ Context-based car action match: '{prompt}' -> {current_car} -> {prompt_clean}")
                    return response

            # Try smart action mapping for common actions
            if car_data:
                action_mappings = {
                    'Ex-showroom Price': ['🚗Ex‑showroom Price', 'Ex-showroom Price', 'Ex‑showroom Price', 'Ex-Showroom Prices'],
                    'Ex‑showroom Price': ['🚗Ex‑showroom Price', 'Ex-showroom Price', 'Ex‑showroom Price', 'Ex-Showroom Prices'],
                    '🚗Ex‑showroom Price': ['🚗Ex‑showroom Price', 'Ex-showroom Price', 'Ex‑showroom Price', 'Ex-Showroom Prices'],
                    'Ex-Showroom Prices': ['🚗Ex‑showroom Price', 'Ex-showroom Price', 'Ex‑showroom Price', 'Ex-Showroom Prices'],
                    'More about this car': ['More about this', 'More about this car'],
                    'Request Brochure': ['Request Brochure', 'Request for Brochure', 'Brochure'],
                    'Book Test Drive': ['Book Test Drive', 'Book a Test Drive']
                }

                if prompt_clean in action_mappings:
                    for mapped_action in action_mappings[prompt_clean]:
                        if mapped_action in car_data:
                            response = get_car_response_with_media_id(current_car, mapped_action)
                            if response:
                                logger.info(f"✅ Context-based smart action match: '{prompt}' -> {current_car} -> {mapped_action}")
                                return response

    # If no context or context didn't match, check all cars
    for car_name in CAR_DATA_REGISTRY.keys():
        if car_name == 'Main Menu':
            continue

        car_data = CAR_DATA_REGISTRY[car_name]

        # Check if prompt matches any action in this car's data
        if prompt_clean in car_data:
            # Use get_car_response_with_media_id with specific car and action
            response = get_car_response_with_media_id(car_name, prompt_clean)
            if response:
                logger.info(f"✅ Car action match: '{prompt}' -> {car_name} -> {prompt_clean}")
                # Set user context
                if user_id:
                    set_user_context(user_id, car_name)
                    logger.info(f"🎯 Set user context: {user_id} -> {car_name}")
                return response

    # PRIORITY 2.5: Handle category queries (Arena, NEXA, All Cars)
    category_result = handle_category_queries(prompt_lower, user_id)
    if category_result:
        logger.info(f"✅ Category query match: '{prompt}'")
        return category_result

    # PRIORITY 2.6: Handle feature queries (car + feature combinations)
    feature_result = handle_feature_queries(prompt_lower, user_id)
    if feature_result:
        return feature_result

    # PRIORITY 2.7: Handle price-based queries (cars under X lakhs, cars from X lakhs)
    if any(phrase in prompt_lower for phrase in ['cars under', 'cars from', 'from', 'under', 'budget', 'starting']):
        if any(price_word in prompt_lower for price_word in ['lakh', 'lakhs', 'l']):
            price_result = handle_price_based_queries(prompt_lower, user_id)
            if price_result:
                logger.info(f"✅ Price-based query match: '{prompt}'")
                return price_result

    # PRIORITY 3: Handle comparison requests using new system with arena_data.py and nexa_data.py
    if any(phrase in prompt_lower for phrase in ['both variants', 'both prices', 'both variant', 'show me both', 'variants of both', 'compare', 'vs', 'versus', 'difference between', 'diffrence between']):
        logger.info(f"🔍 Comparison request detected: '{prompt}'")

        # Use the same car name extraction logic as in generate_detailed_comparison_response
        arena_cars = ['alto k10', 'swift', 'brezza', 'wagon-r', 'wagon r', 'dzire', 'celerio', 's-presso', 's presso', 'ertiga', 'eeco']
        nexa_cars = ['baleno', 'ciaz', 'fronx', 'grand vitara', 'ignis', 'jimny', 'xl6', 'xl-6']
        car_names = arena_cars + nexa_cars

        # Alternative car name mappings for user input variations
        car_name_mappings = {
            'wagon r': 'wagon-r',
            's presso': 's-presso',
            'xl-6': 'xl6'
        }

        # Extract mentioned cars
        mentioned_cars = []
        for car in car_names:
            if car in prompt_lower:
                mentioned_cars.append(car)

        # Check for alternative spellings
        for alt_name, canonical_name in car_name_mappings.items():
            if alt_name in prompt_lower and canonical_name not in mentioned_cars:
                mentioned_cars.append(canonical_name)

        if len(mentioned_cars) >= 2:
            car1, car2 = mentioned_cars[0], mentioned_cars[1]
            logger.info(f"🔍 Comparing: {car1} vs {car2}")

            # Use the dedicated comparison function
            comparison_result = compare_cars_with_data(car1, car2)

            if comparison_result:
                return {
                    "message": comparison_result,
                    "buttons": [],
                    "function_response": []
                }

        # Fallback for comparison requests
        return {
            "message": "🔍 **Car Comparison**\n\nPlease specify which cars you'd like to compare. For example:\n• 'Compare Swift vs Brezza'\n• 'Dzire vs Baleno variants'\n• 'Show me both Swift and Fronx'",
            "data": [],
            "data_type": "text",
            "buttons": []
        }

    # PRIORITY 4: Check for exact matches in WHATSAPP_CONFIG
    if prompt_clean in WHATSAPP_CONFIG:
        logger.info(f"✅ WhatsApp config match: '{prompt}' -> {prompt_clean}")
        return get_whatsapp_response(prompt_clean, user_id)

    # PRIORITY 5: Context-based actions using new system (only if no specific car mentioned)
    if user_id:
        current_car = get_user_context(user_id)
        if current_car:
            # Check if a different car is mentioned in the prompt
            different_car_mentioned = False
            for car_name in CAR_DATA_REGISTRY.keys():
                if car_name != 'Main Menu' and car_name != current_car and car_name.lower() in prompt_lower:
                    different_car_mentioned = True
                    break

            # Also check name mappings for different cars
            if not different_car_mentioned:
                for mapping, car_name in CAR_NAME_MAPPINGS.items():
                    if car_name != current_car and mapping in prompt_lower:
                        different_car_mentioned = True
                        break

            # Only use context if no different car is mentioned
            if not different_car_mentioned:
                # Try to find action in current car's data
                car_data = get_car_data_by_name(current_car)
                if car_data and prompt_clean in car_data:
                    logger.info(f"✅ Context-based match: '{prompt}' -> {current_car} -> {prompt_clean}")
                    return get_whatsapp_response(prompt_clean, user_id)

                # Try common action patterns with smart mapping
                if any(word in prompt_lower for word in ['price', 'variant', 'ex-showroom', 'showroom']):
                    # Use unified price key detection
                    price_key = get_price_key_for_car(car_data)
                    if price_key:
                        logger.info(f"✅ Context-based price match: '{prompt}' -> {current_car} -> {price_key}")
                        return get_whatsapp_response(price_key, user_id)

    # PRIORITY 6: Smart pattern matching using new system
    # Check for car-specific patterns like "more about", "price", etc.
    # Order matters - more specific patterns first!
    # Handle variant/price requests with unified approach
    variant_patterns = ['variants', 'variant', 'variantes', 'price', 'prices', 'ex-showroom', 'showroom']
    if any(pattern in prompt_lower for pattern in variant_patterns):
        logger.info(f"🔍 Variant/price request detected: '{prompt}'")

        # Find which car is mentioned
        detected_car = None

        # Check direct car names first
        for car_name in CAR_DATA_REGISTRY.keys():
            if car_name != 'Main Menu' and car_name.lower() in prompt_lower:
                detected_car = car_name
                break

        # Check name mappings if no direct match
        if not detected_car:
            for mapping, car_name in CAR_NAME_MAPPINGS.items():
                if mapping in prompt_lower:
                    detected_car = car_name
                    break

        # If we found a car, try to get its variants/prices
        if detected_car:
            logger.info(f"🚗 Detected car for variant request: {detected_car}")
            car_data = get_car_data_by_name(detected_car)

            if car_data:
                # Use unified price key detection
                price_key = get_price_key_for_car(car_data)
                if price_key:
                    logger.info(f"✅ Found price key for {detected_car}: {price_key}")
                    response = get_car_response_with_media_id(detected_car, price_key)
                    if response:
                        # Set user context
                        if user_id:
                            set_user_context(user_id, detected_car)
                            logger.info(f"🎯 Set user context: {user_id} -> {detected_car}")
                        return response

    # Handle other patterns
    other_patterns = {
        'more about': ['More about', 'More about this'],
        'tell me about': ['More about', 'More about this'],
        'show me': ['More about', 'More about this'],
        'details of': ['More about', 'More about this'],
        'information about': ['More about', 'More about this'],
        'brochure': ['Brochure', 'Request Brochure', 'Request for Brochure'],
        'test drive': ['Book Test Drive', 'Book a Test Drive']
    }

    for pattern, action_variations in other_patterns.items():
        if pattern in prompt_lower:
            # Find which car is mentioned
            for car_name in CAR_DATA_REGISTRY.keys():
                if car_name != 'Main Menu' and car_name.lower() in prompt_lower:
                    car_data = get_car_data_by_name(car_name)

                    # Try all action variations for this pattern
                    for action_suffix in action_variations:
                        possible_actions = [
                            f"{car_name} {action_suffix}",
                            f"{action_suffix} {car_name}",
                            f"More about {car_name}",
                            action_suffix
                        ]

                        for action in possible_actions:
                            if action in car_data:
                                logger.info(f"✅ Pattern match: '{prompt}' -> {car_name} -> {action}")
                                # Use specific car context instead of general search
                                response = get_car_response_with_media_id(car_name, action)
                                if response:
                                    # Set user context
                                    if user_id:
                                        set_user_context(user_id, car_name)
                                        logger.info(f"🎯 Set user context: {user_id} -> {car_name}")
                                    return response
                    break

            # Also check name mappings
            for mapping, car_name in CAR_NAME_MAPPINGS.items():
                if mapping in prompt_lower:
                    car_data = get_car_data_by_name(car_name)

                    # Try all action variations for this pattern
                    for action_suffix in action_variations:
                        possible_actions = [
                            f"{car_name} {action_suffix}",
                            f"More about {car_name}",
                            action_suffix
                        ]

                        for action in possible_actions:
                            if action in car_data:
                                logger.info(f"✅ Mapping pattern match: '{prompt}' -> {car_name} -> {action}")
                                # Use specific car context instead of general search
                                response = get_car_response_with_media_id(car_name, action)
                                if response:
                                    # Set user context
                                    if user_id:
                                        set_user_context(user_id, car_name)
                                        logger.info(f"🎯 Set user context: {user_id} -> {car_name}")
                                    return response
                    break

    # PRIORITY 7: Check if this should go to LLM (information queries)
    info_keywords = [
        'information', 'info', 'details', 'detail', 'address', 'contact',
        'phone', 'number', 'location', 'where', 'how to', 'tell me about',
        'dealer', 'dealership', 'showroom address', 'showroom contact',
        'working hours', 'timings', 'directions', 'reach'
    ]

    if any(keyword in prompt_lower for keyword in info_keywords):
        logger.info(f"🤖 Routing to LLM: '{prompt}' (detected info query)")
        return None

    return None

def get_whatsapp_response(step_name, user_id=None):
    """
    Get response using new car data system with media_id support - NO NAMESPACING

    Args:
        step_name (str): The step name to get response for (car name or action)
        user_id (str): User ID for context setting

    Returns:
        dict: Formatted response with message, data, and media_id if available
    """
    try:
        # First, check if it's a main menu entry (prioritize main menu over car data)
        if step_name in MAIN_MENU_DATA:
            response_data = MAIN_MENU_DATA[step_name]
            if isinstance(response_data, list) and len(response_data) > 0:
                # Handle main menu entries
                if len(response_data) > 1:
                    # Combine multiple entries into a single response
                    combined_message = ""
                    combined_buttons = []
                    primary_data_type = "button"  # Default to button
                    media_id = ""

                    for entry in response_data:
                        if "message" in entry:
                            if combined_message:
                                combined_message += "\n\n"
                            combined_message += entry["message"]

                        if "data" in entry:
                            combined_buttons.append({
                                "data": entry["data"],
                                "data_type": entry.get("data_type", "list"),
                                "message": entry.get("message", "")
                            })

                        if "Media_ID" in entry and not media_id:
                            media_id = entry["Media_ID"]

                        # Use the data_type from the first entry that has it
                        if "data_type" in entry:
                            primary_data_type = entry["data_type"]

                    response = {
                        "status": "success",
                        "step": step_name,
                        "message": combined_message,
                        "buttons": combined_buttons
                    }

                    if media_id:
                        response["media_id"] = media_id

                    return response
                else:
                    # Single entry
                    step_config = response_data[0]
                    message = step_config.get("message", "")
                    data = step_config.get("data", [])
                    data_type = step_config.get("data_type", "list")
                    media_id = step_config.get("Media_ID", "")

                    response = {
                        "status": "success",
                        "step": step_name,
                        "message": message,
                        "buttons": [{
                            "data": data,
                            "data_type": data_type,
                            "message": message
                        }]
                    }

                    if media_id:
                        response["media_id"] = media_id

                    return response

        # Then, try to get car data directly (no namespacing) - but skip Main Menu
        if step_name != "Main Menu":
            car_response = get_car_response_with_media_id(step_name)
            if car_response:
                # Set user context for car-specific responses
                if user_id:
                    set_user_context(user_id, step_name)
                    logger.info(f"🎯 Set user context: {user_id} -> {step_name}")
                return car_response

        # Check if it's a car action - but prioritize user context if available
        current_car = None
        if user_id:
            current_car = get_user_context(user_id)

        # If user has a current car context, check that car first
        if current_car:
            car_data = get_car_data_by_name(current_car)
            if car_data and step_name in car_data:
                response_data = car_data[step_name]
                if isinstance(response_data, list) and len(response_data) > 0:
                    step_config = response_data[0]
                    message = step_config.get("message", "")
                    data = step_config.get("data", [])
                    data_type = step_config.get("data_type", "list")
                    media_id = step_config.get("Media_ID", "")

                    response = {
                        "status": "success",
                        "step": step_name,
                        "car_name": current_car,
                        "message": message,
                        "buttons": [{
                            "data": data,
                            "data_type": data_type,
                            "message": message
                        }],
                        "hasButtons": len(data) > 0,
                        "data_type": data_type
                    }

                    # Add media_id if present
                    if media_id:
                        response["media_id"] = media_id
                        response["buttons"][0]["media_id"] = media_id

                    logger.info(f"✅ Context-based action match: {current_car} -> {step_name}")
                    return response

        # If no context or context didn't match, check all cars (but this should be rare now)
        for car_name in CAR_DATA_REGISTRY.keys():
            if car_name == 'Main Menu':
                continue

            car_data = CAR_DATA_REGISTRY[car_name]
            if step_name in car_data:
                response_data = car_data[step_name]
                if isinstance(response_data, list) and len(response_data) > 0:
                    step_config = response_data[0]
                    message = step_config.get("message", "")
                    data = step_config.get("data", [])
                    data_type = step_config.get("data_type", "list")
                    media_id = step_config.get("Media_ID", "")

                    response = {
                        "status": "success",
                        "step": step_name,
                        "car_name": car_name,
                        "message": message,
                        "buttons": [{
                            "data": data,
                            "data_type": data_type,
                            "message": message
                        }],
                        "hasButtons": len(data) > 0,
                        "data_type": data_type
                    }

                    # Add media_id if present
                    if media_id:
                        response["media_id"] = media_id
                        response["buttons"][0]["media_id"] = media_id

                    # Set user context
                    if user_id:
                        set_user_context(user_id, car_name)
                        logger.info(f"🎯 Set user context: {user_id} -> {car_name}")

                    logger.info(f"✅ General action match: {car_name} -> {step_name}")
                    return response

        # Fallback to main menu or WHATSAPP_CONFIG
        if step_name in MAIN_MENU_DATA:
            response_data = MAIN_MENU_DATA[step_name]
            if isinstance(response_data, list) and len(response_data) > 0:
                # Handle multiple entries in main menu
                if len(response_data) > 1:
                    # Combine multiple entries into a single response
                    combined_message = ""
                    combined_buttons = []
                    primary_data_type = "button"  # Default to button
                    media_id = ""

                    for i, step_config in enumerate(response_data):
                        message = step_config.get("message", "")
                        data = step_config.get("data", [])
                        data_type = step_config.get("data_type", "list")
                        config_media_id = step_config.get("Media_ID", "")

                        # Combine messages with line breaks
                        if message:
                            if combined_message:
                                combined_message += "\n\n"
                            combined_message += message

                        # Create separate button entries for each step
                        if data:
                            combined_buttons.append({
                                "data": data,
                                "data_type": data_type,
                                "message": message
                            })

                            # Add media_id if present
                            if config_media_id:
                                combined_buttons[-1]["media_id"] = config_media_id
                                if not media_id:  # Use first media_id found
                                    media_id = config_media_id

                        # Use the first data_type as primary
                        if i == 0:
                            primary_data_type = data_type

                    response = {
                        "status": "success",
                        "step": step_name,
                        "message": combined_message,
                        "buttons": combined_buttons,
                        "hasButtons": len(combined_buttons) > 0,
                        "data_type": primary_data_type
                    }

                    # Add media_id if present
                    if media_id:
                        response["media_id"] = media_id

                    return response
                else:
                    # Single entry - original logic
                    step_config = response_data[0]
                    message = step_config.get("message", "")
                    data = step_config.get("data", [])
                    data_type = step_config.get("data_type", "list")
                    media_id = step_config.get("Media_ID", "")

                    response = {
                        "status": "success",
                        "step": step_name,
                        "message": message,
                        "buttons": [{
                            "data": data,
                            "data_type": data_type,
                            "message": message
                        }],
                        "hasButtons": len(data) > 0,
                        "data_type": data_type
                    }

                    # Add media_id if present
                    if media_id:
                        response["media_id"] = media_id
                        response["buttons"][0]["media_id"] = media_id

                    return response

        # Final fallback to old WHATSAPP_CONFIG (for backward compatibility)
        if step_name in WHATSAPP_CONFIG:
            step_data = WHATSAPP_CONFIG[step_name]
            if isinstance(step_data, list) and len(step_data) > 0:
                step_config = step_data[0]
                message = step_config.get("message", "")
                data = step_config.get("data", [])
                data_type = step_config.get("data_type", "list")
                media_id = step_config.get("Media_ID", "")

                response = {
                    "status": "success",
                    "step": step_name,
                    "message": message,
                    "buttons": [{
                        "data": data,
                        "data_type": data_type,
                        "message": message
                    }],
                    "hasButtons": len(data) > 0,
                    "data_type": data_type
                }

                # Add media_id if present
                if media_id:
                    response["media_id"] = media_id
                    response["buttons"][0]["media_id"] = media_id

                return response

        # Not found
        return {
            "status": "error",
            "message": f"Step '{step_name}' not found in any configuration",
            "data": None,
            "data_type": "text"
        }

    except Exception as e:
        logger.error(f"Error in get_whatsapp_response: {str(e)}")
        return {
            "status": "error",
            "message": f"Error getting WhatsApp response: {str(e)}"
        }

def set_user_context(session_id, car_name):
    """Set the current car context for a user session"""
    if session_id:
        USER_CONTEXT[session_id] = {
            'current_car': car_name,
            'timestamp': time.time()
        }

def get_user_context(session_id):
    """Get the current car context for a user session"""
    if session_id and session_id in USER_CONTEXT:
        return USER_CONTEXT[session_id].get('current_car')
    return None

def is_comparison_query(prompt):
    """Check if the prompt is a comparison query"""
    import re
    prompt_lower = prompt.lower()

    # Quick fix: if prompt contains "More about", it's definitely not a comparison
    if "more about" in prompt_lower:
        return False
    comparison_keywords = ['vs', 'versus', 'compare', 'comparison', 'between', 'which is better', 'best', 'difference']
    # Arena cars
    arena_cars = ['alto k10', 'swift', 'brezza', 'wagon-r', 'wagon r', 'dzire', 'celerio', 's-presso', 's presso', 'ertiga', 'eeco']
    # NEXA cars
    nexa_cars = ['baleno', 'ciaz', 'fronx', 'grand vitara', 'ignis', 'jimny', 'xl6', 'xl-6']
    # Tata Commercial vehicles
    tata_commercials = ['ace-pro ev', 'ace pro ev', 'ace-pro petrol', 'ace pro petrol', 'ace pro', 'intra v10', 'intra', 'yodha-cng', 'yodha cng', 'yodha']
    car_names = arena_cars + nexa_cars + tata_commercials

    # Check if it has comparison keywords using word boundaries (except for 'vs' which is often standalone)
    has_comparison = False
    for keyword in comparison_keywords:
        if keyword == 'vs':
            # For 'vs', check if it's a standalone word or surrounded by spaces
            if re.search(r'\bvs\b', prompt_lower):
                has_comparison = True
                break
        else:
            # For other keywords, use word boundaries
            if re.search(r'\b' + re.escape(keyword) + r'\b', prompt_lower):
                has_comparison = True
                break

    # Special check for 'or' - only consider it a comparison keyword if it's between two car names
    if not has_comparison and ' or ' in prompt_lower:
        # Check if 'or' is between two car names
        words = prompt_lower.split()
        for i, word in enumerate(words):
            if word == 'or' and i > 0 and i < len(words) - 1:
                # Check if words before and after 'or' contain car names
                before_text = ' '.join(words[:i])
                after_text = ' '.join(words[i+1:])
                has_car_before = any(car in before_text for car in car_names)
                has_car_after = any(car in after_text for car in car_names)
                if has_car_before and has_car_after:
                    has_comparison = True
                    break

    has_car = any(car in prompt_lower for car in car_names)

    return has_comparison and has_car

def compare_cars_with_data(car1_name, car2_name):
    """Compare two cars using actual data from arena_data.py and nexa_data.py"""
    try:
        # Import both arena and nexa data
        sys.path.insert(0, 'cars')
        from arena_data import cars_data as arena_cars
        from nexa_data import cars_data as nexa_cars

        # Find car data
        car1_data = None
        car2_data = None
        car1_key = None
        car2_key = None

        # Map car names to keys (Arena cars)
        arena_name_to_key = {
            'alto k10': 'alto_k10',
            'brezza': 'brezza',
            'swift': 'swift',
            'wagon-r': 'wagon_r',
            'wagon r': 'wagon_r',
            'dzire': 'dzire',
            'celerio': 'celerio',
            's-presso': 's_presso',
            's presso': 's_presso',
            'ertiga': 'ertiga',
            'eeco': 'eeco'
        }

        # Map car names to keys (NEXA cars)
        nexa_name_to_key = {
            'baleno': 'baleno',
            'ciaz': 'ciaz',
            'fronx': 'fronx',
            'grand vitara': 'grand_vitara',
            'ignis': 'ignis',
            'jimny': 'jimny',
            'xl6': 'xl6',
            'xl-6': 'xl6'
        }

        # Check Arena cars first
        car1_key = arena_name_to_key.get(car1_name.lower())
        car2_key = arena_name_to_key.get(car2_name.lower())

        if car1_key and car1_key in arena_cars:
            car1_data = arena_cars[car1_key]
        elif car1_name.lower() in nexa_name_to_key:
            car1_key = nexa_name_to_key[car1_name.lower()]
            if car1_key in nexa_cars:
                car1_data = nexa_cars[car1_key]

        if car2_key and car2_key in arena_cars:
            car2_data = arena_cars[car2_key]
        elif car2_name.lower() in nexa_name_to_key:
            car2_key = nexa_name_to_key[car2_name.lower()]
            if car2_key in nexa_cars:
                car2_data = nexa_cars[car2_key]

        if not car1_data or not car2_data:
            return None

        # Generate detailed comparison
        comparison = f"🚗 **{car1_data['name']} vs {car2_data['name']} Comparison**\n\n"

        # Category comparison
        comparison += f"📋 **Category:**\n"
        comparison += f"• {car1_data['name']}: {car1_data['category']}\n"
        comparison += f"• {car2_data['name']}: {car2_data['category']}\n\n"

        # Get variants using unified approach
        car1_variants = get_car_variants_unified(car1_data)
        car2_variants = get_car_variants_unified(car2_data)

        # Initialize price lists
        car1_prices = []
        car2_prices = []

        # Price comparison
        if car1_variants and car2_variants:
            # Extract prices from variants (works with both old and new format)
            import re

            def extract_price_from_variant(variant):
                # New format: direct price field
                if 'price' in variant:
                    price_str = variant['price']
                    price_match = re.search(r'(\d+\.?\d*)', price_str)
                    if price_match:
                        return float(price_match.group(1))

                # Old format: price in description
                if 'description' in variant:
                    description = variant['description']
                    price_match = re.search(r'₹([\d.]+)\s*Lakh', description)
                    if price_match:
                        return float(price_match.group(1))

                return 0.0

            car1_prices = [extract_price_from_variant(v) for v in car1_variants if extract_price_from_variant(v) > 0]
            car2_prices = [extract_price_from_variant(v) for v in car2_variants if extract_price_from_variant(v) > 0]

            if car1_prices and car2_prices:
                comparison += f"💰 **Price Range:**\n"
                comparison += f"• {car1_data.get('name', car1_key)}: ₹{min(car1_prices):.2f} - ₹{max(car1_prices):.2f} Lakh\n"
                comparison += f"• {car2_data.get('name', car2_key)}: ₹{min(car2_prices):.2f} - ₹{max(car2_prices):.2f} Lakh\n\n"
            else:
                comparison += f"💰 **Price Range:**\n"
                comparison += f"• {car1_data.get('name', car1_key)}: Price information available on request\n"
                comparison += f"• {car2_data.get('name', car2_key)}: Price information available on request\n\n"
        else:
            comparison += f"💰 **Price Range:**\n"
            comparison += f"• {car1_data.get('name', car1_key)}: Price information available on request\n"
            comparison += f"• {car2_data.get('name', car2_key)}: Price information available on request\n\n"

        # Basic comparison based on available JSON data
        comparison += f"🚗 **Vehicle Type:**\n"
        comparison += f"• {car1_name}: Premium {car1_name} with modern features\n"
        comparison += f"• {car2_name}: Stylish {car2_name} with advanced technology\n\n"

        comparison += f"⭐ **Key Highlights:**\n"
        comparison += f"• {car1_name}: Excellent fuel efficiency and reliability\n"
        comparison += f"• {car2_name}: Superior comfort and performance\n\n"

        # Variants count
        comparison += f"🔢 **Variants Available:**\n"
        comparison += f"• {car1_data.get('name', car1_key)}: {len(car1_variants)} variants\n"
        comparison += f"• {car2_data.get('name', car2_key)}: {len(car2_variants)} variants\n\n"

        # Key features comparison (if available in JSON data)
        comparison += f"✨ **Key Features:**\n"
        car1_name = car1_data.get('name', car1_key)
        car2_name = car2_data.get('name', car2_key)

        # For JSON data, we don't have direct features, so we'll use general info
        comparison += f"• {car1_name}: Premium features and modern technology\n"
        comparison += f"• {car2_name}: Advanced safety and comfort features\n\n"

        # Recommendation
        if car1_prices and car2_prices:
            if min(car1_prices) < min(car2_prices):
                comparison += f"💡 **Quick Insight:** {car1_name} is more budget-friendly, while {car2_name} offers premium features and styling."
            else:
                comparison += f"💡 **Quick Insight:** {car2_name} is more budget-friendly, while {car1_name} offers premium features and styling."
        else:
            comparison += f"💡 **Quick Insight:** Both {car1_name} and {car2_name} offer excellent value in their respective segments. Visit our showroom for detailed pricing and test drives."

        return comparison

    except Exception as e:
        logger.error(f"Error in car comparison: {e}")
        return None

def compare_tata_commercials(car1_name, car2_name):
    """Compare two Tata Commercial vehicles using their specifications"""
    try:
        # Define Tata Commercial vehicle specifications
        tata_specs = {
            'Ace-Pro EV': {
                'name': 'Ace-Pro EV',
                'type': 'Electric Mini-Truck',
                'engine': 'Electric Motor',
                'power': 'Electric Drive',
                'payload': '750 kg',
                'fuel_type': 'Electric',
                'range': 'Up to 154 km',
                'charging': 'Fast charging capability',
                'ideal_for': 'Zero-emission urban deliveries',
                'key_features': ['Zero emissions', 'Low operating cost', 'Silent operation', 'Government incentives']
            },
            'Ace-Pro Petrol': {
                'name': 'Ace-Pro Petrol',
                'type': 'Petrol Mini-Truck',
                'engine': '694 cc Petrol',
                'power': '30 hp @ 4000 rpm',
                'payload': '750 kg',
                'fuel_type': 'Petrol',
                'fuel_efficiency': 'Excellent fuel economy',
                'turning_radius': '~3.75 m',
                'ideal_for': 'Urban last-mile logistics',
                'key_features': ['Compact design', 'Easy maneuverability', 'Low maintenance', 'Reliable performance']
            },
            'Intra V10': {
                'name': 'Intra V10',
                'type': 'Diesel Cargo Truck',
                'engine': '798 cc BS-VI Diesel',
                'power': '~44 hp',
                'payload': '1000 kg',
                'fuel_type': 'Diesel',
                'torque': '110 Nm',
                'turning_radius': '~4.75 m',
                'ideal_for': 'Moderate to heavy load applications',
                'key_features': ['High payload capacity', 'Superior durability', 'Excellent torque', 'Robust build']
            },
            'Yodha-CNG': {
                'name': 'Yodha-CNG',
                'type': 'CNG Pickup Truck',
                'engine': 'CNG Engine',
                'power': 'CNG Optimized',
                'payload': 'High payload capacity',
                'fuel_type': 'CNG',
                'fuel_efficiency': 'Cost-effective CNG operation',
                'eco_friendly': 'Low emissions',
                'ideal_for': 'Long-distance transport',
                'key_features': ['Eco-friendly', 'Cost-effective fuel', 'Long-distance capability', 'Pickup truck design']
            }
        }

        car1_data = tata_specs.get(car1_name)
        car2_data = tata_specs.get(car2_name)

        if not car1_data or not car2_data:
            return None

        comparison = f"🚛 **{car1_data['name']} vs {car2_data['name']} Comparison**\n\n"

        # Vehicle Type
        comparison += f"🚗 **Vehicle Type:**\n"
        comparison += f"• {car1_data['name']}: {car1_data['type']}\n"
        comparison += f"• {car2_data['name']}: {car2_data['type']}\n\n"

        # Engine & Power
        comparison += f"🔧 **Engine & Power:**\n"
        comparison += f"• {car1_data['name']}: {car1_data['engine']} - {car1_data['power']}\n"
        comparison += f"• {car2_data['name']}: {car2_data['engine']} - {car2_data['power']}\n\n"

        # Payload Capacity
        comparison += f"📦 **Payload Capacity:**\n"
        comparison += f"• {car1_data['name']}: {car1_data['payload']}\n"
        comparison += f"• {car2_data['name']}: {car2_data['payload']}\n\n"

        # Fuel Type & Efficiency
        comparison += f"⛽ **Fuel Type:**\n"
        comparison += f"• {car1_data['name']}: {car1_data['fuel_type']}\n"
        comparison += f"• {car2_data['name']}: {car2_data['fuel_type']}\n\n"

        # Ideal Applications
        comparison += f"🎯 **Ideal For:**\n"
        comparison += f"• {car1_data['name']}: {car1_data['ideal_for']}\n"
        comparison += f"• {car2_data['name']}: {car2_data['ideal_for']}\n\n"

        # Key Features
        comparison += f"✨ **Key Features:**\n"
        comparison += f"• **{car1_data['name']}:** {', '.join(car1_data['key_features'])}\n"
        comparison += f"• **{car2_data['name']}:** {', '.join(car2_data['key_features'])}\n\n"

        # Recommendation based on use case
        if car1_data['fuel_type'] == 'Electric' and car2_data['fuel_type'] != 'Electric':
            comparison += f"💡 **Quick Insight:** Choose {car1_data['name']} for zero-emission urban operations with lower running costs, or {car2_data['name']} for traditional fuel flexibility and longer range requirements."
        elif car1_data['fuel_type'] == 'CNG' and car2_data['fuel_type'] != 'CNG':
            comparison += f"💡 **Quick Insight:** Choose {car1_data['name']} for eco-friendly, cost-effective long-distance transport, or {car2_data['name']} for urban delivery applications."
        elif 'payload' in car1_data and 'payload' in car2_data:
            if '1000' in car1_data['payload'] and '750' in car2_data['payload']:
                comparison += f"💡 **Quick Insight:** {car1_data['name']} offers higher payload capacity for heavy-duty applications, while {car2_data['name']} provides better maneuverability for urban deliveries."
            elif '750' in car1_data['payload'] and '1000' in car2_data['payload']:
                comparison += f"💡 **Quick Insight:** {car2_data['name']} offers higher payload capacity for heavy-duty applications, while {car1_data['name']} provides better maneuverability for urban deliveries."
            else:
                comparison += f"💡 **Quick Insight:** Both vehicles offer excellent commercial capabilities. Visit our showroom to test drive and find the perfect match for your business needs."
        else:
            comparison += f"💡 **Quick Insight:** Both {car1_data['name']} and {car2_data['name']} are excellent commercial vehicles. Visit our showroom for detailed specifications and test drives."

        return comparison

    except Exception as e:
        logger.error(f"Error in Tata commercial comparison: {e}")
        return None

def generate_comparison_buttons(prompt):
    """Generate buttons for comparison queries with data-driven comparison"""
    prompt_lower = prompt.lower()
    # Arena cars
    arena_cars = ['alto k10', 'swift', 'brezza', 'wagon-r', 'dzire', 'celerio', 's-presso', 'ertiga', 'eeco']
    # NEXA cars
    nexa_cars = ['baleno', 'ciaz', 'fronx', 'grand vitara', 'ignis', 'jimny', 'xl6']
    car_names = arena_cars + nexa_cars

    # Alternative car name mappings for user input variations
    car_name_mappings = {
        'wagon r': 'wagon-r',
        's presso': 's-presso',
        'xl-6': 'xl6'
    }

    # Extract mentioned cars
    mentioned_cars = []
    for car in car_names:
        if car in prompt_lower:
            # Format car name properly
            formatted_name = car.title().replace(' ', ' ')
            if car == 'alto k10':
                formatted_name = 'Alto K10'
            elif car == 'wagon-r':
                formatted_name = 'Wagon-R'
            elif car == 's-presso':
                formatted_name = 'S-Presso'
            mentioned_cars.append(formatted_name)

    # Check for alternative spellings
    for alt_name, canonical_name in car_name_mappings.items():
        if alt_name in prompt_lower:
            # Format car name properly
            if canonical_name == 'wagon-r':
                formatted_name = 'Wagon-R'
            elif canonical_name == 's-presso':
                formatted_name = 'S-Presso'
            else:
                formatted_name = canonical_name.title()

            if formatted_name not in mentioned_cars:
                mentioned_cars.append(formatted_name)
                

def generate_detailed_comparison_response(prompt):
    """Generate a detailed comparison response using arena, nexa, and tata commercial data"""
    prompt_lower = prompt.lower()
    # Arena cars
    arena_cars = ['alto k10', 'swift', 'brezza', 'wagon-r', 'dzire', 'celerio', 's-presso', 'ertiga', 'eeco']
    # NEXA cars
    nexa_cars = ['baleno', 'ciaz', 'fronx', 'grand vitara', 'ignis', 'jimny', 'xl6']
    # Tata Commercial vehicles
    tata_commercials = ['ace-pro ev', 'ace-pro petrol', 'intra v10', 'yodha-cng']

    car_names = arena_cars + nexa_cars + tata_commercials

    # Alternative car name mappings for user input variations
    car_name_mappings = {
        'wagon r': 'wagon-r',
        's presso': 's-presso',
        'xl-6': 'xl6',
        'ace pro ev': 'ace-pro ev',
        'ace pro petrol': 'ace-pro petrol',
        'ace pro': 'ace-pro petrol',  # Default to petrol
        'intra': 'intra v10',
        'yodha': 'yodha-cng'
    }

    # Extract mentioned cars
    mentioned_cars = []
    for car in car_names:
        if car in prompt_lower:
            mentioned_cars.append(car)

    # Check for alternative spellings
    for alt_name, canonical_name in car_name_mappings.items():
        if alt_name in prompt_lower and canonical_name not in mentioned_cars:
            mentioned_cars.append(canonical_name)

    # If we have exactly 2 cars, generate detailed comparison
    if len(mentioned_cars) >= 2:
        car1_name = mentioned_cars[0]
        car2_name = mentioned_cars[1]

        # Check if both are Tata Commercials
        if car1_name in tata_commercials and car2_name in tata_commercials:
            # Convert to proper case for Tata comparison
            car1_proper = CAR_NAME_MAPPINGS.get(car1_name, car1_name)
            car2_proper = CAR_NAME_MAPPINGS.get(car2_name, car2_name)

            # Find proper names in CAR_DATA_REGISTRY
            for registry_name in CAR_DATA_REGISTRY.keys():
                if car1_name.replace('-', ' ').replace('ace pro', 'ace-pro').lower() in registry_name.lower():
                    car1_proper = registry_name
                if car2_name.replace('-', ' ').replace('ace pro', 'ace-pro').lower() in registry_name.lower():
                    car2_proper = registry_name

            detailed_comparison = compare_tata_commercials(car1_proper, car2_proper)
            if detailed_comparison:
                return detailed_comparison
        else:
            # Use existing comparison for Arena/NEXA cars
            detailed_comparison = compare_cars_with_data(car1_name, car2_name)
            if detailed_comparison:
                return detailed_comparison

    return None

def get_context_aware_response(step_name, session_id=None):
    """
    Get context-aware response that can use common keys with car-specific data

    Args:
        step_name (str): The step name to get response for
        session_id (str): Session ID for context tracking

    Returns:
        dict: Response with status, message, and buttons
    """
    try:
        # Get current car context
        current_car = get_user_context(session_id)

        # Handle common keys with context
        if current_car and step_name in ["Ex-showroom Price", "Request Brochure", "More about this car"]:
            # Map to car-specific key
            car_specific_key = f"{current_car} {step_name}"
            if car_specific_key in WHATSAPP_CONFIG:
                return get_whatsapp_response(car_specific_key)

        # Fall back to regular response
        return get_whatsapp_response(step_name)

    except Exception as e:
        return {
            "status": "error",
            "message": f"Error getting context-aware response: {str(e)}"
        }



# def get_arena_cars_with_buttons():
#     """
#     Get Arena cars with WhatsApp-friendly button format using car data manager
#     """
#     # Get Arena cars from car data manager
#     arena_cars_data = car_data_manager.get_all_arena_cars()
#     if not arena_cars_data:
#         return {"status": "error", "message": "❌ Arena car data not available"}

#     # Convert car data to format expected by utility function
#     arena_cars = []

#     for _, car_data in arena_cars_data.items():
#         car_name = car_data.get('name')

#         arena_cars.append({
#             'name': car_name,
#             'category': car_data.get('category'),
#             'fuel_types': car_data.get('fuel_types', [])
#         })

#     return utility.format_arena_cars_message(arena_cars, {})

# def get_nexa_cars_with_buttons():
#     """
#     Get Nexa cars with WhatsApp-friendly button format using car data manager
#     """
#     # Get Nexa cars from car data manager
#     nexa_cars_data = car_data_manager.get_all_nexa_cars()
#     if not nexa_cars_data:
#         return {"status": "error", "message": "❌ Nexa car data not available"}

#     # Convert car data to format expected by utility function
#     nexa_cars = []

#     for _, car_data in nexa_cars_data.items():
#         car_name = car_data.get('name')

#         nexa_cars.append({
#             'name': car_name,
#             'category': car_data.get('category'),
#             'fuel_types': car_data.get('fuel_types', [])
#         })

#     return utility.format_nexa_cars_message(nexa_cars, {})

def show_car_details_with_buttons(car_name, session_id=None):
    """
    Show car details with action buttons in WhatsApp-friendly format
    Prioritizes JSON flow over car data manager and sets user context
    """
    # Set user context when they select a car
    if session_id:
        set_user_context(session_id, car_name)

    # First check if car exists in JSON config (exact match)
    if car_name in WHATSAPP_CONFIG:
        return get_whatsapp_response(car_name)

    # Check for case-insensitive match in JSON config
    for key in WHATSAPP_CONFIG.keys():
        if key.lower() == car_name.lower():
            if session_id:
                set_user_context(session_id, key)  # Use the exact key as context
            return get_whatsapp_response(key)

    # Check for partial matches in JSON config (for car names)
    car_name_lower = car_name.lower()
    for key in WHATSAPP_CONFIG.keys():
        if car_name_lower in key.lower() or key.lower() in car_name_lower:
            # Additional check to ensure it's likely a car name, not a generic action
            if any(car_word in key.lower() for car_word in ['alto','swift', 'baleno', 'dzire', 'ertiga', 'brezza', 'xl6', 'ciaz', 'ignis', 'fronx', 'wagon']):
                if session_id:
                    set_user_context(session_id, key)  # Use the exact key as context
                return get_whatsapp_response(key)

    # Get car data from car data manager as fallback
    car_data = get_car_data(car_name)

    if car_data:
        # Use car data specifications
        return format_car_details_from_data(car_data, {}, {})

    # If not found, return error
    return {
        "status": "error",
        "message": f"❌ Sorry, '{car_name}' not found in our inventory"
    }



# def get_cars_by_category(category):
#     """
#     Get cars by category (arena/nexa) using cached data
#     """
#     category_lower = category.lower()

#     if category_lower == "arena":
#         return get_arena_cars_with_buttons()
#     elif category_lower == "nexa":
#         return get_nexa_cars_with_buttons()
#     else:
#         return {
#             "status": "error",
#             "message": f"Category '{category}' not found. Please choose Arena or Nexa."
#         }

def get_all_cars():
    """
    Get all cars from both Arena and Nexa using cached data
    """
    all_cars = []

    # Get all cars from car data manager
    all_cached_cars = car_data_manager.get_all_cars()

    # Add Arena cars
    arena_cars = all_cached_cars.get('arena', {})
    for _, car_data in arena_cars.items():
        car_copy = car_data.copy()
        car_copy['dealership_type'] = 'Arena'
        car_copy['dealership_info'] = {}
        all_cars.append(car_copy)

    # Add Nexa cars
    nexa_cars = all_cached_cars.get('nexa', {})
    for _, car_data in nexa_cars.items():
        car_copy = car_data.copy()
        car_copy['dealership_type'] = 'Nexa'
        car_copy['dealership_info'] = {}
        all_cars.append(car_copy)

    return {
        "status": "success",
        "cars": all_cars,
        "total_found": len(all_cars),  # Changed from total_count to total_found for format_search_results_message
        "total_count": len(all_cars),
        "arena_count": len(arena_cars),
        "nexa_count": len(nexa_cars)
    }

def search_cars(query):
    """
    Intelligent car search using AI to understand natural language queries
    No hardcoded patterns - uses AI to extract search criteria
    """
    # Use AI to understand the search intent and extract criteria
    search_criteria = extract_search_criteria_with_ai(query)

    # Get all available cars
    all_cached_cars = car_data_manager.get_all_cars()
    matching_cars = []

    # Search through all cars using AI-extracted criteria
    for category in ['arena', 'nexa']:
        for _, car_data in all_cached_cars.get(category, {}).items():
            if car_matches_criteria(car_data, search_criteria, category):
                enhanced_car = {
                    'name': car_data.get('name', ''),
                    'category': car_data.get('category', ''),
                    'dealership_type': category.title(),
                    'fuel_types': car_data.get('fuel_types', []),
                    'specifications': car_data,
                    'dealership_info': {}
                }
                matching_cars.append(enhanced_car)

    return {
        "status": "success",
        "cars": matching_cars,
        "total_found": len(matching_cars),
        "query": query,
        "search_type": "ai_powered_search",
        "criteria": search_criteria
    }

def extract_search_criteria_with_ai(query):
    """
    Use AI to extract search criteria from natural language query
    """
    try:
        if not model:
            # Fallback to simple extraction if AI not available
            return extract_search_criteria_fallback(query)

        # Create a focused prompt for search criteria extraction
        extraction_prompt = f"""
Analyze this car search query and extract the search criteria in JSON format.

Query: "{query}"

Extract the following criteria if mentioned:
- car_names: List of specific car names mentioned
- fuel_types: List of fuel types (petrol, diesel, cng, hybrid, electric)
- price_range: {{min: number, max: number}} in lakhs (convert any price mentions)
- categories: List of categories (hatchback, sedan, suv, mpv, premium, budget, entry-level)
- features: List of specific features mentioned
- transmission: List of transmission types (manual, automatic, amt, cvt)
- dealership: arena or nexa if specifically mentioned

Return only valid JSON format:
{{
  "car_names": [],
  "fuel_types": [],
  "price_range": {{"min": null, "max": null}},
  "categories": [],
  "features": [],
  "transmission": [],
  "dealership": null,
  "general_intent": "brief description of what user wants"
}}
"""

        # Use a simple chat session for extraction
        chat = model.start_chat()
        response = chat.send_message(extraction_prompt)

        if response.candidates and response.candidates[0].content.parts:
            ai_response = response.candidates[0].content.parts[0].text

            # Try to parse JSON from AI response
            import json
            try:
                # Extract JSON from response (in case AI adds extra text)
                json_start = ai_response.find('{')
                json_end = ai_response.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_str = ai_response[json_start:json_end]
                    criteria = json.loads(json_str)
                    # Post-process AI results for better user experience
                    criteria = post_process_ai_criteria(criteria, query)
                    return criteria
            except json.JSONDecodeError:
                logger.warning(f"Could not parse AI response as JSON: {ai_response}")

    except Exception as e:
        error_msg = str(e)
        if "429" in error_msg or "quota" in error_msg.lower():
            logger.warning(f"AI extraction rate limited: {e}")
            # Add small delay for rate limiting
            time.sleep(1)
        else:
            logger.warning(f"AI extraction failed: {e}")

    # Fallback to simple extraction
    return extract_search_criteria_fallback(query)

def post_process_ai_criteria(criteria, query):
    """
    Post-process AI-extracted criteria to make them more user-friendly
    """
    query_lower = query.lower()
    price_range = criteria.get("price_range", {})

    # Handle "starting from" queries - add reasonable upper bound
    if (price_range.get("min") and not price_range.get("max") and
        ('from starting' in query_lower or 'starting from' in query_lower or
         ('starting' in query_lower and 'lakh' in query_lower))):

        base_price = price_range["min"]
        # Set reasonable upper bound (2.5x the starting price)
        criteria["price_range"]["max"] = base_price * 2.5
        logger.info(f"Added upper bound for 'starting from' query: {base_price} -> {base_price * 2.5}")

    return criteria

def extract_search_criteria_fallback(query):
    """
    Fallback method for extracting search criteria without AI
    """
    query_lower = query.lower()
    criteria = {
        "car_names": [],
        "fuel_types": [],
        "price_range": {"min": None, "max": None},
        "categories": [],
        "features": [],
        "transmission": [],
        "dealership": None,
        "general_intent": query
    }

    # Enhanced price extraction with user-friendly logic
    price_numbers = re.findall(r'(\d+)l?', query_lower)
    if len(price_numbers) >= 2 and ('to' in query_lower or '-' in query_lower):
        criteria["price_range"]["min"] = float(price_numbers[0])
        criteria["price_range"]["max"] = float(price_numbers[1])
    elif price_numbers and ('under' in query_lower or 'below' in query_lower):
        criteria["price_range"]["max"] = float(price_numbers[0])
    elif price_numbers and ('above' in query_lower or 'over' in query_lower):
        criteria["price_range"]["min"] = float(price_numbers[0])
    elif price_numbers and ('from starting' in query_lower or 'starting from' in query_lower or 'starting' in query_lower):
        # For "starting from X", set a reasonable upper bound to avoid showing premium cars
        base_price = float(price_numbers[0])
        criteria["price_range"]["min"] = base_price
        criteria["price_range"]["max"] = base_price * 2.5  # Reasonable upper bound

    # Simple fuel type detection
    if 'petrol' in query_lower:
        criteria["fuel_types"].append('petrol')
    if 'diesel' in query_lower:
        criteria["fuel_types"].append('diesel')
    if 'cng' in query_lower:
        criteria["fuel_types"].append('cng')
    if 'hybrid' in query_lower:
        criteria["fuel_types"].append('hybrid')

    # Enhanced car name detection using new system
    # Extract car names using CAR_NAME_MAPPINGS
    for mapping, car_name in CAR_NAME_MAPPINGS.items():
        if mapping in query_lower and car_name not in criteria["car_names"]:
            criteria["car_names"].append(car_name)

    # Also check direct car names from registry
    for car_name in CAR_DATA_REGISTRY.keys():
        if car_name != 'Main Menu' and car_name.lower() in query_lower:
            if car_name not in criteria["car_names"]:
                criteria["car_names"].append(car_name)

    return criteria

def car_matches_criteria(car_data, criteria, category):
    """
    Check if a car matches the extracted search criteria
    """
    # If specific car names are mentioned, check for exact match
    if criteria.get("car_names"):
        car_name = car_data.get('name', '').lower()
        for search_name in criteria["car_names"]:
            if search_name.lower() in car_name or car_name in search_name.lower():
                return True
        return False  # If specific names mentioned but no match, exclude

    # Check fuel types
    if criteria.get("fuel_types"):
        car_fuel_types = [ft.lower() for ft in car_data.get('fuel_types', [])]
        for search_fuel in criteria["fuel_types"]:
            search_fuel_lower = search_fuel.lower()
            # Handle hybrid matching
            if search_fuel_lower == 'hybrid' or search_fuel_lower == 'petrol':
                if any('hybrid' in ft or 'petrol' in ft for ft in car_fuel_types):
                    break
            elif any(search_fuel_lower in ft for ft in car_fuel_types):
                break
        else:
            return False  # No fuel type match found

    # Check price range
    price_range = criteria.get("price_range", {})
    if price_range.get("min") or price_range.get("max"):
        # Try to find variants in the new data structure
        variants = car_data.get('variants', [])

        # If no variants found, try the new format with car name + " variants"
        if not variants:
            car_name = car_data.get('name', '')
            # Try different variant key formats
            for key in car_data.keys():
                if key.endswith(' variants'):
                    variants = car_data[key]
                    break

        price_match = False
        for variant in variants:
            # Handle both 'price' and 'price_range' fields
            price_str = variant.get('price', '') or variant.get('price_range', '')
            price_numbers = re.findall(r'[\d.]+', price_str)
            if price_numbers:
                car_price = float(price_numbers[0])
                min_price = price_range.get("min")
                max_price = price_range.get("max")

                # Check if car price matches the criteria
                matches_min = not min_price or car_price >= min_price
                matches_max = not max_price or car_price <= max_price

                if matches_min and matches_max:
                    price_match = True
                    break

        if not price_match:
            return False

    # Check dealership preference
    if criteria.get("dealership"):
        if criteria["dealership"].lower() != category.lower():
            return False

    # If no specific criteria matched but no exclusions either, include the car
    return True

def is_specific_car_query(query):
    """
    Check if the query is asking for a specific car name vs. general search criteria
    Uses the new CAR_DATA_REGISTRY and CAR_NAME_MAPPINGS
    """
    query_lower = query.lower()

    # Check against car registry
    for car_name in CAR_DATA_REGISTRY.keys():
        if car_name != 'Main Menu' and car_name.lower() in query_lower:
            return True

    # Check against name mappings
    for mapping in CAR_NAME_MAPPINGS.keys():
        if mapping in query_lower:
            return True

    # If query contains general search terms, it's not specific
    general_terms = [
        'cars', 'petrol', 'diesel', 'hybrid', 'cng', 'automatic', 'manual',
        'under', 'above', 'below', 'lakhs', 'budget', 'premium', 'fuel',
        'mileage', 'price', 'range', 'show me', 'find', 'search'
    ]

    # If query is mostly general terms, it's not specific
    general_word_count = sum(1 for term in general_terms if term in query_lower)
    total_words = len(query_lower.split())

    # If more than 50% of words are general terms, treat as general search
    if total_words > 0 and (general_word_count / total_words) > 0.5:
        return False

    return False  # Default to general search for safety


@app.route('/generate', methods=['POST'])
def generate_text():
    global SESSION_ID, model

    # ✅ Helper to detect greetings
    def is_greeting(prompt):
        greetings = {"hi wic", "hello", "hey", "hii", "hi!", "hey!", "start", "menu", "reset", "greet", "hi"}
        return prompt.strip().lower() in greetings

    response_data = {
        "text": "",
        "function_response_id": "",
        "function_response": [],
        "llm": ""
    }

    try:
        data = request.get_json(silent=True) or {}
        prompt = data.get('prompt')
        user_id = data.get('user_id')

        if not prompt or not user_id:
            return jsonify({'error': 'Missing prompt or user_id in request body'}), 400

        prompt = prompt.strip()
        function_response = ""

        # Initialize session if needed
        if user_id not in SESSION_ID:
            SESSION_ID[user_id] = []

        # ✅ Main menu trigger: greeting only (not new user)
        if is_greeting(prompt):
            SESSION_ID[user_id] = []  # reset session
            greeting_response = get_whatsapp_response("Main Menu")
            response_data["text"] = greeting_response["message"]
            response_data["llm"] = greeting_response["message"]
            response_data["function_response_id"] = 1
            response_data["function_response"] = greeting_response.get("buttons", [])

            # Add media_id if present in greeting response
            if "media_id" in greeting_response:
                response_data["media_id"] = greeting_response["media_id"]

        else:
            logger.info(f"🔍 Processing non-greeting prompt: '{prompt}'")

            # Check for comparison queries FIRST (highest priority)
            if is_comparison_query(prompt):
                # Try to generate detailed comparison using arena data
                detailed_comparison = generate_detailed_comparison_response(prompt)
                if detailed_comparison:
                    response_data["text"] = detailed_comparison
                    response_data["llm"] = detailed_comparison

                    # Add comparison buttons
                    comparison_buttons = generate_comparison_buttons(prompt)
                    if comparison_buttons:
                        response_data["function_response"] = comparison_buttons
                        response_data["function_response_id"] = 1
                else:
                    response_data["text"] = "❌ Sorry, I couldn't generate a comparison for those cars. Please try with different car names."
                    response_data["llm"] = response_data["text"]

            # Check if it's a car-specific action request (format: "CarName|Action")
            elif "|" in prompt:
                logger.info(f"🔧 Detected car-specific action format: '{prompt}'")
                parts = prompt.split("|", 1)
                if len(parts) == 2:
                    car_name = parts[0].strip()
                    action = parts[1].strip()

                    logger.info(f"🚗 Car-specific action: {car_name} -> {action}")

                    # Try to handle car-specific action using new car data system (NO NAMESPACING)
                    car_response = get_car_response_with_media_id(car_name, action)

                    if car_response:
                        response_data["text"] = car_response["message"]
                        response_data["llm"] = car_response["message"]
                        response_data["function_response_id"] = 1
                        response_data["function_response"] = car_response.get("buttons", [])

                        # Add media_id if present
                        if "media_id" in car_response:
                            response_data["media_id"] = car_response["media_id"]

                        logger.info(f"✅ Found car action using new system: {car_name} -> {action}")
                    else:
                        # Fall through to regular flow if car-specific action fails
                        whatsapp_response = check_whatsapp_flow_match(prompt, user_id)
                        if whatsapp_response:
                            response_data["text"] = whatsapp_response["message"]
                            response_data["llm"] = whatsapp_response["message"]
                            response_data["function_response_id"] = 1
                            response_data["function_response"] = whatsapp_response.get("buttons", [])
                            # Add media_id if present in whatsapp response
                            if "media_id" in whatsapp_response:
                                response_data["media_id"] = whatsapp_response["media_id"]
                else:
                    # Invalid format, fall through to regular flow
                    whatsapp_response = check_whatsapp_flow_match(prompt, user_id)
                    if whatsapp_response:
                        response_data["text"] = whatsapp_response["message"]
                        response_data["llm"] = whatsapp_response["message"]
                        response_data["function_response_id"] = 1
                        response_data["function_response"] = whatsapp_response.get("buttons", [])
                        # Add media_id if present in whatsapp response
                        if "media_id" in whatsapp_response:
                            response_data["media_id"] = whatsapp_response["media_id"]
            else:
                logger.info(f"🔍 Processing regular prompt (no | format): '{prompt}'")
                # Check for comparison queries first (before WhatsApp flow matching)
                if is_comparison_query(prompt):
                    # Try to generate detailed comparison using arena data
                    detailed_comparison = generate_detailed_comparison_response(prompt)
                    if detailed_comparison:
                        logger.info("✅ Generated detailed comparison successfully")
                        response_data["text"] = detailed_comparison
                        response_data["llm"] = detailed_comparison

                        # Add comparison buttons
                        comparison_buttons = generate_comparison_buttons(prompt)
                        if comparison_buttons:
                            response_data["function_response"] = comparison_buttons
                            response_data["function_response_id"] = 1
                    else:
                        logger.warning("❌ Detailed comparison failed, falling back to LLM")
                        # If detailed comparison fails, fall back to LLM
                        if model is None:
                            response_data["text"] = "❌ *Service Temporarily Unavailable*\n\nThe AI assistant is currently not available. Please try again later."
                            response_data["llm"] = response_data["text"]
                        else:
                            chat_session = model.start_chat(history=SESSION_ID[user_id])
                            response = chat_session.send_message(prompt)

                            if response.candidates and response.candidates[0].content.parts:
                                part = response.candidates[0].content.parts[0]
                                response_data["text"] = part.text
                                response_data["llm"] = part.text
                            else:
                                response_data["text"] = "❌ AI could not generate a response. Please try again."
                                response_data["llm"] = response_data["text"]

                        # Still add comparison buttons even if detailed comparison fails
                        comparison_buttons = generate_comparison_buttons(prompt)
                        if comparison_buttons:
                            response_data["function_response"] = comparison_buttons
                            response_data["function_response_id"] = 1
                else:
                    # Check if the prompt matches any WhatsApp flow step directly
                    logger.info(f"🔍 Checking WhatsApp flow match for: '{prompt}'")
                    whatsapp_response = check_whatsapp_flow_match(prompt, user_id)
                    if whatsapp_response:
                        logger.info(f"✅ Found WhatsApp flow match for: '{prompt}'")
                        response_data["text"] = whatsapp_response["message"]
                        response_data["llm"] = whatsapp_response["message"]
                        response_data["function_response_id"] = 1
                        response_data["function_response"] = whatsapp_response.get("buttons", [])
                    else:
                        logger.info(f"❌ No WhatsApp flow match found for: '{prompt}'")

                        # Check for vehicle type queries (van, truck, lorry, etc.)
                        vehicle_type_response = handle_vehicle_type_queries(prompt.lower(), user_id)
                        if vehicle_type_response:
                            logger.info(f"✅ Found vehicle type match for: '{prompt}'")
                            response_data["text"] = vehicle_type_response["message"]
                            response_data["llm"] = vehicle_type_response["message"]
                            response_data["function_response_id"] = 1
                            response_data["function_response"] = vehicle_type_response.get("buttons", [])
                        # Check for comparison queries before calling LLM
                        elif is_comparison_query(prompt):
                            logger.info(f"🔍 Detected comparison query before LLM: '{prompt}'")
                            # Try to generate detailed comparison using arena data
                            detailed_comparison = generate_detailed_comparison_response(prompt)
                            if detailed_comparison:
                                logger.info("✅ Generated detailed comparison successfully (before LLM)")
                                response_data["text"] = detailed_comparison
                                response_data["llm"] = detailed_comparison

                                # Add comparison buttons
                                comparison_buttons = generate_comparison_buttons(prompt)
                                if comparison_buttons:
                                    response_data["function_response"] = comparison_buttons
                                    response_data["function_response_id"] = 1
                            else:
                                logger.warning("❌ Detailed comparison failed, proceeding with LLM")
                                # Fall through to LLM
                                if model is None:
                                    response_data["text"] = "❌ *Service Temporarily Unavailable*\n\nThe AI assistant is currently not available. Please try again later."
                                    response_data["llm"] = response_data["text"]
                                else:
                                    # Call LLM as fallback
                                    chat_session = model.start_chat(history=SESSION_ID[user_id])
                                    response = chat_session.send_message(prompt)

                                    if response.candidates and response.candidates[0].content.parts:
                                        part = response.candidates[0].content.parts[0]
                                        response_data["text"] = part.text
                                        response_data["llm"] = part.text
                                    else:
                                        response_data["text"] = "❌ AI could not generate a response. Please try again."
                                        response_data["llm"] = response_data["text"]
                        else:
                            # Use Gemini AI for all other inputs
                            if model is None:
                                response_data["text"] = "❌ *Service Temporarily Unavailable*\n\nThe AI assistant is currently not available. Please try again later."
                                response_data["llm"] = response_data["text"]
                            else:
                                chat_session = model.start_chat(history=SESSION_ID[user_id])
                            response = chat_session.send_message(prompt)

                            if not response.candidates or not response.candidates[0].content.parts:
                                response_data["text"] = "❌ AI could not generate a response. Please try again."
                                response_data["llm"] = response_data["text"]
                                return jsonify(response_data)

                            part = response.candidates[0].content.parts[0]

                            # Check for comparison queries before processing function calls
                            if is_comparison_query(prompt):
                                logger.info(f"🔍 Detected comparison query in LLM response: '{prompt}'")
                                # Try to generate detailed comparison using arena data
                                detailed_comparison = generate_detailed_comparison_response(prompt)
                                if detailed_comparison:
                                    logger.info("✅ Generated detailed comparison successfully (overriding LLM function call)")
                                    response_data["text"] = detailed_comparison
                                    response_data["llm"] = detailed_comparison

                                    # Add comparison buttons
                                    comparison_buttons = generate_comparison_buttons(prompt)
                                    if comparison_buttons:
                                        response_data["function_response"] = comparison_buttons
                                        response_data["function_response_id"] = 1
                                else:
                                    logger.warning("❌ Detailed comparison failed, proceeding with LLM function call")

                            elif hasattr(part, "function_call") and part.function_call:
                                fn = part.function_call.name
                                args = part.function_call.args

                                if fn == "show_car_details":
                                    car_id = args.get("car_id", "")

                                    # First check if this car exists in WhatsApp flow
                                    whatsapp_car_response = check_whatsapp_flow_match(car_id, user_id)
                                    if whatsapp_car_response:
                                        function_response = whatsapp_car_response.get("message", "")
                                        response_data["text"] = function_response
                                        response_data["llm"] = function_response
                                        response_data["function_response_id"] = 1
                                        response_data["function_response"] = whatsapp_car_response.get("buttons", [])
                                    else:
                                        # Fallback to car data manager
                                        result = show_car_details_with_buttons(car_id, user_id)  # Pass session_id
                                        function_response = result.get("message", "")
                                        response_data["text"] = function_response
                                        response_data["llm"] = function_response
                                        # Always show buttons for car details to ensure WhatsApp flow works
                                        if result.get("buttons"):
                                            response_data["function_response_id"] = 1
                                            response_data["function_response"] = result["buttons"]

                                elif fn == "get_cars_by_category":
                                    category = args.get("category", "")

                                    # Check WhatsApp flow first
                                    whatsapp_category_response = None
                                    if category.lower() == "arena":
                                        whatsapp_category_response = check_whatsapp_flow_match("Maruti_Suzuki_Arena", user_id)
                                    elif category.lower() == "nexa":
                                        whatsapp_category_response = check_whatsapp_flow_match("NEXA", user_id)

                                    if whatsapp_category_response:
                                        function_response = whatsapp_category_response.get("message", "")
                                        response_data["text"] = function_response
                                        response_data["llm"] = function_response
                                        response_data["function_response_id"] = 1
                                        response_data["function_response"] = whatsapp_category_response.get("buttons", [])
                                    else:
                                        # Fallback to car data manager
                                        # if category.lower() == "arena":
                                        #     result = get_arena_cars_with_buttons()
                                        # elif category.lower() == "nexa":
                                        #     result = get_nexa_cars_with_buttons()
                                        # else:
                                        #     result = {"message": "Invalid category. Please choose Arena or Nexa."}
                                        # function_response = result.get("message", "")
                                        # response_data["text"] = function_response
                                        # response_data["llm"] = function_response
                                        # Always show buttons for car categories to ensure WhatsApp flow works
                                        if result.get("buttons"):
                                            response_data["function_response_id"] = 1
                                            response_data["function_response"] = result["buttons"]

                                elif fn == "search_cars":
                                    query = args.get("query", "")

                                    # Only check WhatsApp flow for specific car names, not general search terms
                                    whatsapp_match = None
                                    if is_specific_car_query(query):
                                        whatsapp_match = check_whatsapp_flow_match(query, user_id)

                                    if whatsapp_match:
                                        function_response = whatsapp_match.get("message", "")
                                        response_data["text"] = function_response
                                        response_data["llm"] = function_response
                                        response_data["function_response_id"] = 1
                                        response_data["function_response"] = whatsapp_match.get("buttons", [])
                                    else:
                                        # Check for price-based queries before AI search
                                        price_result = handle_price_based_queries(query.lower(), user_id)
                                        if price_result:
                                            response_data["text"] = price_result["message"]
                                            response_data["llm"] = price_result["message"]
                                            response_data["function_response_id"] = 1
                                            response_data["function_response"] = price_result.get("buttons", [])
                                        else:
                                            # Use AI-powered search
                                            result = search_cars(query)

                                            if result["status"] == "success" and result["cars"]:
                                                formatted_response = utility.format_search_results_message(result)
                                                function_response = formatted_response["message"]
                                                response_data["text"] = function_response
                                                response_data["llm"] = function_response
                                                # Always show buttons for search results to ensure WhatsApp flow works
                                                if formatted_response.get("buttons"):
                                                    response_data["function_response_id"] = 1
                                                    response_data["function_response"] = formatted_response["buttons"]
                                            else:
                                                # Let LLM handle no results naturally with search context
                                                function_response = f"No cars found matching '{query}'. Search criteria: {result.get('criteria', {})}"
                                                response_data["text"] = function_response
                                                response_data["llm"] = function_response

                                elif fn == "get_all_cars":
                                    # Get all cars from both Arena and Nexa
                                    logger.info("🚗 Calling get_all_cars() function")
                                    result = get_all_cars()
                                    logger.info(f"🚗 get_all_cars() result: status={result.get('status')}, cars_count={len(result.get('cars', []))}")

                                    if result["status"] == "success" and result["cars"]:
                                        logger.info("✅ get_all_cars() success - formatting response")
                                        # Format the response with interactive buttons using the dedicated function
                                        formatted_response = format_all_cars_message(result)
                                        function_response = formatted_response["message"]
                                        response_data["text"] = function_response
                                        response_data["llm"] = function_response
                                        # Always show buttons for all cars to ensure WhatsApp flow works
                                        if formatted_response.get("buttons"):
                                
                                            response_data["function_response_id"] = 1
                                            response_data["function_response"] = formatted_response["buttons"]
                                    else:
                                        logger.warning(f"❌ get_all_cars() failed: status={result.get('status')}, cars={len(result.get('cars', []))}")
                                        function_response = "❌ No cars available at the moment"
                                        response_data["text"] = function_response
                                        response_data["llm"] = function_response

                                else:
                                    function_response = (
                                        "❌ Sorry, I could not understand your request.\n\n"
                                        "Please try: 'hi' for main menu, 'arena' or 'nexa' for categories, or specific car names like 'swift', 'baleno'."
                                    )
                                    response_data["text"] = function_response
                                    response_data["llm"] = function_response

                            else:
                                # Plain text fallback
                                response_data["text"] = part.text
                                response_data["llm"] = part.text



        # Save to history
        SESSION_ID[user_id].append({"role": "user", "parts": [prompt]})
        SESSION_ID[user_id].append({"role": "model", "parts": [response_data["llm"]]})

        final_response = {
            'generated_text': response_data.get("text", ""),
            'function_response_id': response_data.get("function_response_id", ""),
            'function_response': response_data.get("function_response", [])
        }

        # Add media_id to final response if it exists in response_data
        if "media_id" in response_data:
            final_response["media_id"] = response_data["media_id"]

        return jsonify(final_response)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/test', methods=['GET'])
def test_endpoint():
    """Simple test endpoint"""
    return jsonify({"status": "success", "message": "API is working!"})

@app.route('/api/car-action', methods=['POST'])
def car_action():
    """
    Handle car-specific actions with common action names

    Request format:
    {
        "car_name": "Swift",
        "action": "Ex-showroom Price",
        "session_id": "optional"
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        car_name = data.get('car_name', '').strip()
        action = data.get('action', '').strip()

        if not car_name or not action:
            return jsonify({"error": "car_name and action are required"}), 400

        logger.info(f"🚗 Car action request: {car_name} -> {action}")

        # Use the new car data system (NO NAMESPACING)
        car_response = get_car_response_with_media_id(car_name, action)

        if car_response:
            response = {
                "status": "success",
                "step": f"{car_name}_{action}",
                "car_name": car_name,
                "action": action,
                "message": car_response["message"],
                "buttons": car_response.get("buttons", []),
                "hasButtons": car_response.get("hasButtons", False),
                "data_type": car_response.get("data_type", "list")
            }

            # Add media_id if present
            if "media_id" in car_response:
                response["media_id"] = car_response["media_id"]

            logger.info(f"✅ Found car action using new system: {car_name} -> {action}")
            return jsonify(response)
        else:
            # Action not found, get available actions from car data
            car_data = get_car_data_by_name(car_name)
            available_actions = list(car_data.keys()) if car_data else []

            logger.warning(f"❌ Action '{action}' not found for {car_name}")

            return jsonify({
                "status": "error",
                "message": f"Action '{action}' not available for {car_name}",
                "available_actions": available_actions,
                "car_name": car_name,
                "requested_action": action
            })

    except Exception as e:
        logger.error(f"❌ Error in car-action endpoint: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/cars-actions', methods=['GET'])
def get_cars_actions():
    """
    Get all cars and their available actions using new car data system
    """
    try:
        cars_with_actions = list_all_cars_with_actions()

        return jsonify({
            "status": "success",
            "cars_count": len(cars_with_actions),
            "cars": cars_with_actions,
            "total_actions": sum(len(actions) for actions in cars_with_actions.values()),
            "message": "Successfully retrieved all cars with their available actions"
        })

    except Exception as e:
        logger.error(f"❌ Error in cars-actions endpoint: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/car-actions/<car_name>', methods=['GET'])
def get_car_actions(car_name):
    """
    Get available actions for a specific car
    """
    try:
        actions = get_available_car_actions(car_name)

        if actions:
            return jsonify({
                "status": "success",
                "car_name": car_name,
                "actions_count": len(actions),
                "actions": actions,
                "message": f"Found {len(actions)} actions for {car_name}"
            })
        else:
            return jsonify({
                "status": "not_found",
                "car_name": car_name,
                "message": f"No actions found for car '{car_name}'",
                "available_cars": list(list_all_cars_with_actions().keys())
            }), 404

    except Exception as e:
        logger.error(f"❌ Error in car-actions endpoint: {str(e)}")
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8037, debug=False)