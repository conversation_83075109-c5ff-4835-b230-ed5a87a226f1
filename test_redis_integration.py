#!/usr/bin/env python3
"""
Test script to verify Redis integration with the car data system
"""

import json
import redis

def test_redis_connection():
    """Test Redis connection and data retrieval"""
    try:
        # Connect to Redis
        redis_client = redis.Redis(host='localhost', port=6379, db=0)
        redis_client.ping()
        print("✅ Redis connection successful")
        
        # Get all keys
        keys = redis_client.keys('*')
        print(f"🔑 Available Redis keys: {[k.decode('utf-8') for k in keys]}")
        
        # Test fetching Alto K10 data
        alto_data = redis_client.get('alto_k10')
        if alto_data:
            json_data = json.loads(alto_data.decode('utf-8'))
            print(f"✅ Alto K10 data loaded successfully")
            print(f"📋 Alto K10 keys: {list(json_data.keys())[:5]}...")  # Show first 5 keys
        else:
            print("❌ Alto K10 data not found")
            
        # Test fetching Tata commercial data
        ace_data = redis_client.get('ace-pro_ev')
        if ace_data:
            json_data = json.loads(ace_data.decode('utf-8'))
            print(f"✅ Ace-Pro EV data loaded successfully")
            print(f"📋 Ace-Pro EV keys: {list(json_data.keys())[:5]}...")  # Show first 5 keys
        else:
            print("❌ Ace-Pro EV data not found")
            
        return True
        
    except Exception as e:
        print(f"❌ Redis test failed: {e}")
        return False

def test_car_data_functions():
    """Test the car data functions from the main module"""
    try:
        # Import the functions we need
        from bhandari_auto_api_v4 import load_car_data_from_redis, get_car_data_from_redis_by_name
        
        print("\n🧪 Testing car data functions...")
        
        # Test direct Redis loading
        alto_data = load_car_data_from_redis('alto_k10')
        if alto_data:
            print("✅ load_car_data_from_redis('alto_k10') works")
        else:
            print("❌ load_car_data_from_redis('alto_k10') failed")
            
        # Test name-based loading
        alto_data2 = get_car_data_from_redis_by_name('Alto K10')
        if alto_data2:
            print("✅ get_car_data_from_redis_by_name('Alto K10') works")
        else:
            print("❌ get_car_data_from_redis_by_name('Alto K10') failed")
            
        return True
        
    except Exception as e:
        print(f"❌ Car data function test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Redis integration tests...\n")
    
    # Test 1: Redis connection and data
    redis_ok = test_redis_connection()
    
    # Test 2: Car data functions
    if redis_ok:
        functions_ok = test_car_data_functions()
        
        if functions_ok:
            print("\n🎉 All tests passed! Redis integration is working correctly.")
        else:
            print("\n⚠️ Redis connection works but car data functions have issues.")
    else:
        print("\n❌ Redis connection failed. Please check your Redis server.")
